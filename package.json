{"name": "amplify-vite-react-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-amplify/ui-react": "^6.5.5", "@aws-sdk/client-dynamodb": "^3.751.0", "@aws-sdk/lib-dynamodb": "^3.751.0", "@azure/storage-blob": "^12.27.0", "@elastic/elasticsearch": "^8.17.1", "@google/genai": "^1.16.0", "@opensearch-project/opensearch": "^3.3.0", "aws-amplify": "^6.6.6", "axios": "^1.7.9", "jsonwebtoken": "^9.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "0.34.1", "typechat": "^0.1.1"}, "devDependencies": {"@aws-amplify/backend": "^1.5.0", "@aws-amplify/backend-cli": "^1.2.9", "@dotenvx/dotenvx": "^1.38.3", "@types/jsonwebtoken": "^9.0.10", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "aws-cdk": "^2.138.0", "aws-cdk-lib": "^2.138.0", "constructs": "^10.3.0", "dotenv": "^16.4.7", "esbuild": "^0.20.2", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "tsx": "^4.7.2", "typescript": "^5.4.5", "vite": "^5.4.10"}}