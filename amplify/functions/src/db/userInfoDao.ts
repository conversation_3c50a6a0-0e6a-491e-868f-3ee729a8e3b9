import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import { UserStatus } from "../config/enums";

const dynamoClient = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(dynamoClient);

const USER_INFO_TABLE = "user_info_monova_dev";

export interface UserInfo {
  userId: string;
  userFullName: string;
  userWhatsAppNumber: string;
  userStatus: UserStatus;
  userRole: string;
  token: string;
  createdAt: string;
  updatedAt: string;
}

export class UserInfoDao {
  constructor() {
    console.log("Initializing UserInfoDao...");
  }

  async createUserInfo(
    userId: string,
    userFullName: string,
    userWhatsAppNumber: string,
    userStatus: UserStatus,
    userRole: string,
    token: string
  ): Promise<UserInfo> {
    console.log("Creating user info:", { userId, userFullName, userWhatsAppNumber, userStatus, userRole });

    try {
      const now = new Date().toISOString();
      const item: UserInfo = {
        userId,
        userFullName,
        userWhatsAppNumber,
        userStatus,
        userRole,
        token,
        createdAt: now,
        updatedAt: now,
      };

      await docClient.send(
        new PutCommand({
          TableName: USER_INFO_TABLE,
          Item: item,
        })
      );

      console.log("User info created successfully:", item);
      return item;
    } catch (error) {
      console.error("Error creating user info:", error);
      throw error;
    }
  }

  async getUserInfoByUserId(userId: string): Promise<UserInfo | null> {
    console.log("Getting user info by userId:", userId);

    try {
      const result = await docClient.send(
        new GetCommand({
          TableName: USER_INFO_TABLE,
          Key: { userId },
        })
      );

      if (!result.Item) {
        console.log("User info not found for userId:", userId);
        return null;
      }

      console.log("User info retrieved successfully:", result.Item);
      return result.Item as UserInfo;
    } catch (error) {
      console.error("Error getting user info:", error);
      throw error;
    }
  }

  async updateUserInfo(
    userId: string,
    updates: Partial<Omit<UserInfo, "userId" | "createdAt">>
  ): Promise<UserInfo> {
    console.log("Updating user info:", { userId, updates });

    try {
      const updateExpression: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {};

      updates.updatedAt = new Date().toISOString();

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          updateExpression.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      const result = await docClient.send(
        new UpdateCommand({
          TableName: USER_INFO_TABLE,
          Key: { userId },
          UpdateExpression: `SET ${updateExpression.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: "ALL_NEW",
        })
      );

      console.log("User info updated successfully:", result.Attributes);
      return result.Attributes as UserInfo;
    } catch (error) {
      console.error("Error updating user info:", error);
      throw error;
    }
  }

  async deleteUserInfo(userId: string): Promise<{ userId: string; deleted: boolean }> {
    console.log("Deleting user info:", userId);

    try {
      await docClient.send(
        new DeleteCommand({
          TableName: USER_INFO_TABLE,
          Key: { userId },
        })
      );

      console.log("User info deleted successfully:", userId);
      return { userId, deleted: true };
    } catch (error) {
      console.error("Error deleting user info:", error);
      throw error;
    }
  }
}