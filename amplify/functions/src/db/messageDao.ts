import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  QueryCommand,
  UpdateCommand,
  GetCommand,
} from "@aws-sdk/lib-dynamodb";
import { MessagePlatform, MessageOrigin } from "../config/enums";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client);

const TABLE_NAME = "messages_monova_test";

export class MessageDao {
  async createMessage(
    caseId: string,
    messageId: string,
    userId: string,
    messageDirection: "INBOUND" | "OUTBOUND",
    messageStatus: "SENT" | "DELIVERED" | "READ" | "RECEIVED",
    messageType: "TEXT" | "IMAGE" | "INTERACTIVE" | "LOG",
    messageContent: any,
    messagePlatform: MessagePlatform,
    messageOrigin: MessageOrigin,
    messageContext?: any
  ) {
    console.log("=== Creating Message Record ===", {
      caseId,
      messageId,
      userId,
      messageDirection,
      messageStatus,
      messageType,
      messageContent,
      messagePlatform,
      messageOrigin,
    });

    try {
      let contentToStore = messageContent;

      // Step 1: Handle message content based on type
      if (messageType === "TEXT") {
        messageContent = messageContent?.body?.body; // Store only the body for text messages
      }

      // Step 2: Handle image messages
      if (messageType === "IMAGE") {
        if (messageContent?.image?.id) {
          console.log(
            `[messageDao][createMessage] tid=${messageId} Processing image message with media ID:`,
            messageContent.image.id
          );
          // Store the image metadata
          contentToStore = {
            mediaId: messageContent.image.id,
            mimeType: messageContent.image.mime_type,
            sha256: messageContent.image.sha256,
            caption: messageContent.image.caption || "",
          };
        } else if (messageContent?.url) {
          // Handle direct URL images
          contentToStore = {
            url: messageContent.url,
            caption: messageContent.caption || "",
          };
        }
      }

      // Step 3: Handle interactive messages
      if (messageType === "INTERACTIVE") {
        if (messageContent?.interactive?.button_reply) {
          contentToStore = {
            type: "button_reply",
            id: messageContent.interactive.button_reply.id,
            title: messageContent.interactive.button_reply.title,
          };
        } else if (messageContent?.interactive?.list_reply) {
          contentToStore = {
            type: "list_reply",
            id: messageContent.interactive.list_reply.id,
            title: messageContent.interactive.list_reply.title,
            description: messageContent.interactive.list_reply.description,
          };
        }
      }

      const createdAt = Date.now();
      console.log("Attempting to create message record in database");

      const params = {
        TableName: TABLE_NAME,
        Item: {
          caseId,
          messageId,
          userId,
          createdAt,
          messageDirection,
          messageStatus,
          messageType,
          messageContent: JSON.stringify(contentToStore),
          messagePlatform,
          messageOrigin,
        },
      };
      await docClient.send(new PutCommand(params));
      return messageId;
    } catch (error) {
      console.error(
        `[messageDao][createMessage] tid=${messageId} error -> ${error}`
      );
      throw error;
    }
  }

  async getMessagesByCaseId(caseId: string) {
    console.log("Getting messages by case ID:", caseId);
    try {
      const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: "caseId = :caseId",
        ExpressionAttributeValues: {
          ":caseId": caseId,
        },
        ScanIndexForward: true, // Sort by sort key in ascending order
      };

      const { Items } = await docClient.send(new QueryCommand(params));
      console.log("Messages retrieved successfully:", {
        caseId,
        messageCount: Items?.length || 0,
      });

      return Items || [];
    } catch (error) {
      console.error("Error getting messages by case ID:", error);
      throw error;
    }
  }

  async updateMessageStatuses(
    statusUpdates: Array<{
      caseId: string;
      messageId: string;
      messageStatus: "DELIVERED" | "READ";
    }>
  ) {
    console.log("Updating message statuses:", statusUpdates);
    try {
      const updatePromises = statusUpdates.map(async (update) => {
        const params = {
          TableName: TABLE_NAME,
          Key: {
            caseId: update.caseId,
            messageId: update.messageId,
          },
          UpdateExpression: "SET messageStatus = :status, updatedAt = :updatedAt",
          ExpressionAttributeValues: {
            ":status": update.messageStatus,
            ":updatedAt": Date.now(),
          },
        };

        return docClient.send(new UpdateCommand(params));
      });

      await Promise.all(updatePromises);
      console.log("Message statuses updated successfully");
      return statusUpdates;
    } catch (error) {
      console.error("Error updating message statuses:", error);
      throw error;
    }
  }

  async getMessageById(caseId: string, messageId: string) {
    console.log("Getting message by ID:", { caseId, messageId });
    try {
      const params = {
        TableName: TABLE_NAME,
        Key: {
          caseId,
          messageId,
        },
      };

      const { Item } = await docClient.send(new GetCommand(params));
      console.log("Message retrieved successfully:", Item);
      return Item;
    } catch (error) {
      console.error("Error getting message by ID:", error);
      throw error;
    }
  }
}
