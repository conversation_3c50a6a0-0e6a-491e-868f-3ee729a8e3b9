import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import { CaseStatus } from "../config/enums";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client);

const CASE_TABLE = "cases_monova_test";

export interface CaseData {
  userId: string;
  caseId: string;
  caseStartTime: string;
  caseStatus: CaseStatus;
  messages: string[];
  useCase?: string;
  contextReady?: boolean;
  userOutfitImage?: string;
  createdAt: number;
  updatedAt: number;
}

export class CaseDao {
  async createCase(
    userId: string,
    caseId: string,
    caseStartTime: string,
    caseStatus: CaseStatus,
    messages: string[] = []
  ): Promise<CaseData> {
    console.log("[CaseDao][createCase] Creating case:", { userId, caseId, caseStatus });

    try {
      const now = Date.now();
      const item: CaseData = {
        userId,
        caseId,
        caseStartTime,
        caseStatus,
        messages,
        contextReady: false,
        createdAt: now,
        updatedAt: now,
      };

      await docClient.send(
        new PutCommand({
          TableName: CASE_TABLE,
          Item: item,
        })
      );

      console.log("[CaseDao][createCase] Created case:", item);
      return item;
    } catch (error) {
      console.error("[CaseDao][createCase] Error:", error);
      throw error;
    }
  }

  async getCaseById(userId: string, caseId: string): Promise<CaseData | null> {
    console.log("[CaseDao][getCaseById] Getting case:", { userId, caseId });

    try {
      const result = await docClient.send(
        new GetCommand({
          TableName: CASE_TABLE,
          Key: {
            userId,
            caseId,
          },
        })
      );

      if (!result.Item) {
        console.log("[CaseDao][getCaseById] Case not found");
        return null;
      }

      console.log("[CaseDao][getCaseById] Found case:", result.Item);
      return result.Item as CaseData;
    } catch (error) {
      console.error("[CaseDao][getCaseById] Error:", error);
      throw error;
    }
  }

  async updateCase(
    userId: string,
    caseId: string,
    updates: Partial<Omit<CaseData, "userId" | "caseId" | "createdAt">>
  ): Promise<CaseData> {
    console.log("[CaseDao][updateCase] Updating case:", { userId, caseId, updates });

    try {
      const updateExpression: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, any> = {};

      // Always update the updatedAt timestamp
      updates.updatedAt = Date.now();

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          updateExpression.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      const result = await docClient.send(
        new UpdateCommand({
          TableName: CASE_TABLE,
          Key: {
            userId,
            caseId,
          },
          UpdateExpression: `SET ${updateExpression.join(", ")}`,
          ExpressionAttributeNames: expressionAttributeNames,
          ExpressionAttributeValues: expressionAttributeValues,
          ReturnValues: "ALL_NEW",
        })
      );

      console.log("[CaseDao][updateCase] Updated case:", result.Attributes);
      return result.Attributes as CaseData;
    } catch (error) {
      console.error("[CaseDao][updateCase] Error:", error);
      throw error;
    }
  }

  async listCasesByUser(
    userId: string,
    limit: number = 50,
    startKey?: any
  ): Promise<{ items: CaseData[]; lastEvaluatedKey?: any }> {
    console.log("[CaseDao][listCasesByUser] Listing cases:", { userId, limit });

    try {
      const params: any = {
        TableName: CASE_TABLE,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: {
          ":userId": userId,
        },
        Limit: limit,
        ScanIndexForward: false, // Get most recent first
      };

      if (startKey) {
        params.ExclusiveStartKey = startKey;
      }

      const result = await docClient.send(new QueryCommand(params));

      console.log("[CaseDao][listCasesByUser] Found cases:", {
        count: result.Items?.length || 0,
        hasMore: !!result.LastEvaluatedKey,
      });

      return {
        items: (result.Items || []) as CaseData[],
        lastEvaluatedKey: result.LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[CaseDao][listCasesByUser] Error:", error);
      throw error;
    }
  }

  async deleteCase(userId: string, caseId: string): Promise<void> {
    console.log("[CaseDao][deleteCase] Deleting case:", { userId, caseId });

    try {
      await docClient.send(
        new DeleteCommand({
          TableName: CASE_TABLE,
          Key: {
            userId,
            caseId,
          },
        })
      );

      console.log("[CaseDao][deleteCase] Deleted case successfully");
    } catch (error) {
      console.error("[CaseDao][deleteCase] Error:", error);
      throw error;
    }
  }
}
