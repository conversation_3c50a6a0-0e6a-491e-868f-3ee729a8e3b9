import { MessagePlatform, MessageOrigin } from "../../config/enums";
import { AIService } from "../../ai/integrations/AIIntegration";
import { StyleProfileService } from "../../services/styleProfileService";
import { ApparelService } from "../../services/apparelService";
import { ChatService } from "../../services/chatService";
import { CaseService } from "../../services/caseService";
import { outfitCurationPrompt } from "../../ai/prompts/outfitCurationPrompt";
import { outfitCurationWithExistingApparelsPrompt } from "../../ai/prompts/outfitCurationWithExistingApparelsPrompt";

export class OutfitUtils {
  private static aiService = new AIService();
  private static styleProfileService = new StyleProfileService();
  private static apparelService = new ApparelService();
  private static caseService = new CaseService();
  private static chatService = new ChatService(this.caseService);

  /**
   * Generates outfit recommendations using AI and user's style profile
   */
  static async generateOutfit(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    context?: {
      occasion?: string;
      weather?: string;
      preferences?: string[];
    }
  ) {
    console.log(
      `[OutfitUtils][generateOutfit] tid=${messageId} Generating outfit:`,
      { caseId, userId, messagePlatform, context }
    );

    try {
      // Get user's style profile
      const styleProfile = await this.styleProfileService.getProfile(userId);

      if (!styleProfile) {
        console.log(
          `[OutfitUtils][generateOutfit] tid=${messageId} No style profile found, using basic generation`
        );
        return this.generateBasicOutfit(userId, caseId, messageId, messagePlatform, context);
      }

      // Get user's wardrobe
      const wardrobe = await this.apparelService.listApparels(userId);
      const hasWardrobe = wardrobe && wardrobe.length > 0;

      // Prepare context for AI
      const outfitContext = {
        occasion: context?.occasion || "casual",
        weather: context?.weather || "mild",
        preferences: context?.preferences || [],
        userProfile: {
          gender: styleProfile.userGender,
          bodyType: styleProfile.userBodyType,
          age: styleProfile.userAge,
          undertone: styleProfile.userUndertone,
          height: styleProfile.userHeight,
        },
      };

      let prompt: string;
      let wardrobeInfo = "";

      if (hasWardrobe) {
        // Use existing wardrobe prompt
        wardrobeInfo = wardrobe
          .map((item: any) => `${item.apparelCategory}: ${item.apparelDescription}`)
          .join("\n");

        prompt = `${outfitCurationWithExistingApparelsPrompt}

USER PROFILE:
Gender: ${outfitContext.userProfile.gender}
Body Type: ${outfitContext.userProfile.bodyType}
Age: ${outfitContext.userProfile.age}
Undertone: ${outfitContext.userProfile.undertone}
Height: ${outfitContext.userProfile.height}

CONTEXT:
Occasion: ${outfitContext.occasion}
Weather: ${outfitContext.weather}
Preferences: ${outfitContext.preferences.join(", ")}

EXISTING WARDROBE:
${wardrobeInfo}

Please generate a personalized outfit recommendation using the user's existing wardrobe items when possible.`;
      } else {
        // Use general outfit curation prompt
        prompt = `${outfitCurationPrompt}

USER PROFILE:
Gender: ${outfitContext.userProfile.gender}
Body Type: ${outfitContext.userProfile.bodyType}
Age: ${outfitContext.userProfile.age}
Undertone: ${outfitContext.userProfile.undertone}
Height: ${outfitContext.userProfile.height}

CONTEXT:
Occasion: ${outfitContext.occasion}
Weather: ${outfitContext.weather}
Preferences: ${outfitContext.preferences.join(", ")}

Please generate a personalized outfit recommendation based on the user's profile and context.`;
      }

      // Generate outfit using AI
      const aiResponse = await this.aiService.getGeminiTextAnalysis(
        prompt,
        "You are Monova, an expert AI Fashion Stylist. Generate personalized outfit recommendations.",
        0.7
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Outfit generated successfully`
      );

      // Send the outfit recommendation to the user
      try {
        await this.chatService.sendMessage({
          recipientNumber: userId, // WhatsApp number is the userId
          messageType: "TEXT",
          messagePlatform: messagePlatform,
          content: {
            text: aiResponse,
          },
          messageOrigin: MessageOrigin.MODEL,
          caseId,
        });

        console.log(
          `[OutfitUtils][generateOutfit] tid=${messageId} Outfit recommendation sent to user successfully`
        );
      } catch (sendError) {
        console.error(
          `[OutfitUtils][generateOutfit] tid=${messageId} Error sending outfit recommendation:`,
          { sendError, userId, caseId }
        );
        // Continue with the response even if sending fails
      }

      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "OUTFIT_GENERATED",
          outfit: {
            recommendation: aiResponse,
            context: outfitContext,
            hasWardrobe,
            generatedAt: new Date().toISOString(),
          },
        }),
      };
    } catch (error) {
      console.error(
        `[OutfitUtils][generateOutfit] tid=${messageId} Error generating outfit:`,
        { error, caseId, userId }
      );

      // Fallback to basic outfit generation
      return this.generateBasicOutfit(userId, caseId, messageId, messagePlatform, context);
    }
  }

  /**
   * Generates a basic outfit when style profile is not available
   */
  private static async generateBasicOutfit(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    context?: {
      occasion?: string;
      weather?: string;
      preferences?: string[];
    }
  ) {
    console.log(
      `[OutfitUtils][generateBasicOutfit] tid=${messageId} Generating basic outfit`
    );

    const basicPrompt = `Generate a stylish outfit recommendation for:
Occasion: ${context?.occasion || "casual"}
Weather: ${context?.weather || "mild"}
Preferences: ${context?.preferences?.join(", ") || "comfortable and stylish"}

Please provide a complete outfit with top, bottom, and footwear suggestions.`;

    try {
      const aiResponse = await this.aiService.getGeminiTextAnalysis(
        basicPrompt,
        "You are Monova, an expert AI Fashion Stylist. Generate stylish outfit recommendations.",
        0.7
      );

      // Send the basic outfit recommendation to the user
      try {
        await this.chatService.sendMessage({
          recipientNumber: userId, // WhatsApp number is the userId
          messageType: "TEXT",
          messagePlatform: messagePlatform,
          content: {
            text: aiResponse,
          },
          messageOrigin: MessageOrigin.MODEL,
          caseId,
        });

        console.log(
          `[OutfitUtils][generateBasicOutfit] tid=${messageId} Basic outfit recommendation sent to user successfully`
        );
      } catch (sendError) {
        console.error(
          `[OutfitUtils][generateBasicOutfit] tid=${messageId} Error sending basic outfit recommendation:`,
          { sendError, userId, caseId }
        );
        // Continue with the response even if sending fails
      }

      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "OUTFIT_GENERATED",
          outfit: {
            recommendation: aiResponse,
            context: context || {},
            hasWardrobe: false,
            generatedAt: new Date().toISOString(),
          },
        }),
      };
    } catch (error) {
      console.error(
        `[OutfitUtils][generateBasicOutfit] tid=${messageId} Error:`,
        error
      );

      // Final fallback
      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "OUTFIT_GENERATED",
          outfit: {
            recommendation: "I'd be happy to help you with outfit suggestions! Could you tell me more about the occasion and your style preferences?",
            context: context || {},
            hasWardrobe: false,
            generatedAt: new Date().toISOString(),
          },
        }),
      };
    }
  }
}
