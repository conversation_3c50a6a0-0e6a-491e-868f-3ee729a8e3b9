import { MessagePlatform, MessageOrigin } from "../../config/enums";
import { AIService } from "../../ai/integrations/AIIntegration";
import { ChatService } from "../../services/chatService";
import { CaseService } from "../../services/caseService";

export class AmaUtils {
  // Maximum number of messages allowed in AMA chat
  private static readonly MESSAGE_LIMIT = 30;
  private static aiService = new AIService();
  private static caseService = new CaseService();
  private static chatService = new ChatService(this.caseService);

  static async handleAmaChat(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    messages: any[]
  ) {
    console.log(
      `[AmaUtils][handleAmaChat] tid=${messageId} Handling AMA chat:`,
      { caseId, userId, messageCount: messages.length }
    );

    try {
      // Check message limit
      if (messages.length >= this.MESSAGE_LIMIT) {
        console.log(
          `[AmaUtils][handleAmaChat] tid=${messageId} Message limit reached`
        );
        return {
          statusCode: 200,
          type: "LIMIT_REACHED",
          body: JSON.stringify({
            message: "You've reached the maximum number of messages for this conversation.",
          }),
        };
      }

      // Get the latest user message
      const latestMessage = messages[messages.length - 1];
      const userQuestion = latestMessage?.content || "";

      console.log(
        `[AmaUtils][handleAmaChat] tid=${messageId} Processing user question:`,
        { userQuestion }
      );

      // Create a conversation context from recent messages
      const conversationHistory = messages
        .slice(-5) // Get last 5 messages for context
        .map((msg: any) => `${msg.role}: ${msg.content}`)
        .join("\n");

      // Create AMA prompt
      const amaPrompt = `You are Monova, an expert AI Fashion Stylist. You're having a casual conversation with a user who has fashion-related questions.

Conversation history:
${conversationHistory}

Latest question: ${userQuestion}

Please provide a helpful, friendly, and personalized fashion advice response. Keep it conversational and engaging. If the question is not fashion-related, gently redirect the conversation back to fashion and style topics.`;

      // Generate AI response using Gemini
      const aiResponse = await this.aiService.getGeminiTextAnalysis(
        amaPrompt,
        "You are Monova, an expert AI Fashion Stylist. Provide helpful and engaging fashion advice.",
        0.7
      );

      console.log(
        `[AmaUtils][handleAmaChat] tid=${messageId} AMA response generated successfully`
      );

      // Send the AI response to the user
      try {
        await this.chatService.sendMessage({
          recipientNumber: userId, // WhatsApp number is the userId
          messageType: "TEXT",
          messagePlatform: messagePlatform,
          content: {
            text: aiResponse,
          },
          messageOrigin: MessageOrigin.MODEL,
          caseId,
        });

        console.log(
          `[AmaUtils][handleAmaChat] tid=${messageId} AMA response sent to user successfully`
        );
      } catch (sendError) {
        console.error(
          `[AmaUtils][handleAmaChat] tid=${messageId} Error sending AMA response:`,
          { sendError, userId, caseId }
        );
        // Continue with the response even if sending fails
      }

      return {
        statusCode: 200,
        type: "MESSAGE_PROCESSED",
        body: JSON.stringify({
          message: aiResponse,
        }),
      };
    } catch (error) {
      console.error(
        `[AmaUtils][handleAmaChat] tid=${messageId} Error in AMA chat:`,
        { error, caseId, userId }
      );

      // Fallback response on error
      return {
        statusCode: 200,
        type: "MESSAGE_PROCESSED",
        body: JSON.stringify({
          message: "I'm here to help with your fashion questions! Could you please rephrase your question?",
        }),
      };
    }
  }
}
