import { AIService } from "../../ai/integrations/AIIntegration";
import { TypeChatService } from "../../ai/integrations/TypeChatIntegration";
import { CaseService } from "../../services/caseService";
import { ChatService } from "../../services/chatService";

export class CaseContextUtils {
  constructor(
    private aiService: AIService,
    private caseService: CaseService,
    private typeChatService: TypeChatService,
    private chatService: ChatService
  ) {
    console.log("[CaseContextUtils] Initializing CaseContextUtils...");
  }

  /**
   * Gathers case context from conversation messages
   */
  async gatherCaseContext(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    formattedMessages: any[]
  ) {
    console.log(
      `[CaseContextUtils][gatherCaseContext] tid=${messageId} Gathering case context:`,
      { caseId, userId, messageCount: formattedMessages.length }
    );

    try {
      // Extract conversation content
      const conversationText = formattedMessages
        .map((msg: any) => `${msg.role}: ${msg.content}`)
        .join("\n");

      // Create context extraction prompt
      const contextPrompt = `Analyze this fashion conversation and extract key context information:

${conversationText}

Please identify and extract:
1. Occasion/Event type (e.g., work, casual, formal, party, date, etc.)
2. Time of day/season preferences
3. Weather considerations
4. Style preferences mentioned
5. Color preferences
6. Any specific requirements or constraints
7. Budget considerations (if mentioned)
8. Body concerns or preferences (if mentioned)

Return the context in a structured format that can be used for outfit recommendations.`;

      // Use AI to extract context
      const contextAnalysis = await this.aiService.getGeminiTextAnalysis(
        contextPrompt,
        "You are an expert at analyzing fashion conversations and extracting context.",
        0.3
      );

      // Parse the AI response to extract structured context
      const extractedContext = this.parseContextFromAI(contextAnalysis);

      console.log(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} Context gathered:`,
        extractedContext
      );

      // Update case with gathered context
      await this.caseService.updateCase({
        caseId,
        userId,
        contextReady: true,
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "CONTEXT_GATHERED",
          context: extractedContext,
        }),
      };
    } catch (error) {
      console.error(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} Error gathering context:`,
        { error, caseId, userId }
      );

      // Fallback to basic context
      const fallbackContext = {
        eventType: "casual",
        eventTime: "day",
        weather: "mild",
        preferences: ["comfortable", "stylish"],
        extracted: false,
      };

      return {
        statusCode: 200,
        body: JSON.stringify({
          type: "CONTEXT_GATHERED",
          context: fallbackContext,
        }),
      };
    }
  }

  /**
   * Parses AI response to extract structured context
   */
  private parseContextFromAI(aiResponse: string): any {
    try {
      // Try to extract key information from the AI response
      const context: any = {
        eventType: "casual",
        eventTime: "day",
        weather: "mild",
        preferences: [],
        colors: [],
        requirements: [],
        extracted: true,
      };

      const lowerResponse = aiResponse.toLowerCase();

      // Extract occasion/event type
      if (lowerResponse.includes("work") || lowerResponse.includes("office") || lowerResponse.includes("professional")) {
        context.eventType = "work";
      } else if (lowerResponse.includes("formal") || lowerResponse.includes("elegant") || lowerResponse.includes("dressy")) {
        context.eventType = "formal";
      } else if (lowerResponse.includes("party") || lowerResponse.includes("celebration") || lowerResponse.includes("event")) {
        context.eventType = "party";
      } else if (lowerResponse.includes("date") || lowerResponse.includes("romantic")) {
        context.eventType = "date";
      }

      // Extract time preferences
      if (lowerResponse.includes("evening") || lowerResponse.includes("night")) {
        context.eventTime = "evening";
      } else if (lowerResponse.includes("morning")) {
        context.eventTime = "morning";
      }

      // Extract weather considerations
      if (lowerResponse.includes("cold") || lowerResponse.includes("winter") || lowerResponse.includes("chilly")) {
        context.weather = "cold";
      } else if (lowerResponse.includes("hot") || lowerResponse.includes("summer") || lowerResponse.includes("warm")) {
        context.weather = "hot";
      }

      // Extract style preferences
      const styleKeywords = ["comfortable", "stylish", "trendy", "classic", "minimalist", "bohemian", "edgy", "feminine", "masculine"];
      context.preferences = styleKeywords.filter(keyword => lowerResponse.includes(keyword));

      // Extract color preferences
      const colorKeywords = ["black", "white", "blue", "red", "green", "yellow", "pink", "purple", "brown", "gray", "navy", "beige"];
      context.colors = colorKeywords.filter(color => lowerResponse.includes(color));

      return context;
    } catch (error) {
      console.error("Error parsing context from AI response:", error);
      return {
        eventType: "casual",
        eventTime: "day",
        weather: "mild",
        preferences: ["comfortable", "stylish"],
        extracted: false,
      };
    }
  }
}
