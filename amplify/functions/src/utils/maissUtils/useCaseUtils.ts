import { CaseStatus, MessagePlatform, UseCaseResponseType } from "../../config/enums";
import { AIService } from "../../ai/integrations/AIIntegration";

export interface UseCaseResponse {
  type: "INTENT_CLASSIFIED" | "INTENT_REQUIRED";
  message: string;
  options: Array<string>;
  intent?: string;
  imageUrl?: string;
}

export class UseCaseUtils {
  private static aiService = new AIService();

  /**
   * Identifies the use case from user messages
   */
  static async identifyUseCase(
    formattedMessages: any[],
    caseId: string,
    userId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: any
  ): Promise<UseCaseResponse> {
    console.log(
      `[UseCaseUtils][identifyUseCase] tid=${messageId} Identifying use case:`,
      { caseId, userId, messageCount: formattedMessages.length }
    );

    try {
      // Extract the latest user message
      const latestMessage = formattedMessages[formattedMessages.length - 1];
      const messageContent = latestMessage?.content || "";

      console.log(
        `[UseCaseUtils][identifyUseCase] tid=${messageId} Analyzing message:`,
        { messageContent }
      );

      // Use AI to classify intent
      const intentPrompt = `Analyze this user message and classify the intent:

Message: "${messageContent}"

Classify the intent as one of:
- AMA: General fashion questions and advice
- OUTFIT_GENERATION: User wants outfit recommendations
- OUTFIT_REVIEW: User wants feedback on their outfit
- STYLE_PROFILE: User wants to update their style preferences

Respond with just the intent category (AMA, OUTFIT_GENERATION, OUTFIT_REVIEW, or STYLE_PROFILE).`;

      const intentResponse = await this.aiService.getGeminiTextAnalysis(
        intentPrompt,
        "You are an expert at classifying fashion-related user intents.",
        0.1
      );

      let intent = "AMA"; // Default fallback
      let message = "I'm here to help with your fashion questions!";

      // Parse the AI response
      const classifiedIntent = intentResponse.trim().toUpperCase();
      if (["AMA", "OUTFIT_GENERATION", "OUTFIT_REVIEW", "STYLE_PROFILE"].includes(classifiedIntent)) {
        intent = classifiedIntent;

        // Set appropriate message based on intent
        switch (intent) {
          case "OUTFIT_GENERATION":
            message = "I'd love to help you create the perfect outfit! Tell me about the occasion and your preferences.";
            break;
          case "OUTFIT_REVIEW":
            message = "I'm ready to review your outfit! Please share a photo and I'll give you detailed feedback.";
            break;
          case "STYLE_PROFILE":
            message = "Let's update your style profile! What would you like to change about your preferences?";
            break;
          default:
            message = "I'm here to help with your fashion questions!";
        }
      }

      const response: UseCaseResponse = {
        type: "INTENT_CLASSIFIED",
        message,
        options: Object.values(CaseStatus),
        intent,
      };

      console.log(
        `[UseCaseUtils][identifyUseCase] tid=${messageId} Use case identified:`,
        response
      );

      return response;
    } catch (error) {
      console.error(
        `[UseCaseUtils][identifyUseCase] tid=${messageId} Error identifying use case:`,
        { error, caseId, userId }
      );

      // Fallback to AMA on error
      return {
        type: "INTENT_CLASSIFIED",
        message: "I'm here to help with your fashion questions!",
        options: Object.values(CaseStatus),
        intent: "AMA",
      };
    }
  }

  /**
   * Handles case use case setup
   */
  static async handleCaseUsecaseSetup(
    caseId: string,
    userId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    skipFollowUp: boolean,
    messages?: any
  ): Promise<UseCaseResponse> {
    console.log(
      `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Processing usecase setup:`,
      { caseId, userId, skipFollowUp }
    );

    try {
      // For now, return a mock response
      // In a full implementation, this would process the messages and determine the use case
      const mockResponse: UseCaseResponse = {
        type: "INTENT_CLASSIFIED",
        message: "Intent classified as AMA",
        options: Object.values(CaseStatus),
        intent: "AMA",
      };

      console.log(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Use case setup completed (mock):`,
        mockResponse
      );

      return mockResponse;
    } catch (error) {
      console.error(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Error in use case setup:`,
        { error, caseId, userId }
      );
      throw error;
    }
  }

  /**
   * Updates case intent
   */
  private static async updateCaseIntent(
    caseId: string,
    userId: string,
    caseStatus: CaseStatus,
    messageId: string,
    intent: string,
    imageUrl?: string
  ) {
    console.log(
      `[UseCaseUtils][updateCaseIntent] tid=${messageId} Updating case intent:`,
      { caseId, userId, caseStatus, intent, imageUrl }
    );

    try {
      // For now, just log the update
      // In a full implementation, this would update the CaseService
      console.log(
        `[UseCaseUtils][updateCaseIntent] tid=${messageId} Case intent updated successfully (mock)`
      );
    } catch (error) {
      console.error(
        `[UseCaseUtils][updateCaseIntent] tid=${messageId} Error updating case intent:`,
        { error, caseId, userId, intent }
      );
      throw error;
    }
  }

  /**
   * Sends a message to the user
   */
  private static async sendMessage(
    userId: string,
    caseId: string,
    message: string,
    messagePlatform: MessagePlatform,
    messageId: string
  ) {
    console.log(
      `[UseCaseUtils][sendMessage] tid=${messageId} Sending message:`,
      { userId, caseId, messagePlatform }
    );

    try {
      // For now, just log the message
      // In a full implementation, this would use ChatService to send the message
      console.log(
        `[UseCaseUtils][sendMessage] tid=${messageId} Message: ${message}`
      );
      console.log(
        `[UseCaseUtils][sendMessage] tid=${messageId} Message sent successfully (mock)`
      );
    } catch (error) {
      console.error(
        `[UseCaseUtils][sendMessage] tid=${messageId} Error sending message:`,
        { error, userId, caseId, message }
      );
      throw error;
    }
  }

  /**
   * Extracts image URL from message
   */
  private static extractImageUrl(message: any): string | undefined {
    console.log("[UseCaseUtils][extractImageUrl] Extracting image URL from message");

    try {
      // For now, return undefined
      // In a full implementation, this would parse the message content for image URLs
      return undefined;
    } catch (error) {
      console.error("[UseCaseUtils][extractImageUrl] Error extracting image URL:", error);
      return undefined;
    }
  }
}
