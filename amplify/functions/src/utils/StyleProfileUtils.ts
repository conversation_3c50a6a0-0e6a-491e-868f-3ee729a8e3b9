import {
  MessageDirection,
  MessageOrigin,
  MessagePlatform,
  UserRole,
} from "../config/enums";
import { UserInfoDao } from "../db/userInfoDao";
import { ChatService } from "../services/chatService";
import { StyleProfileService } from "../services/styleProfileService";
import { CreateStyleProfileInput, UpdateStyleProfileInput, Gender, BodyType, Undertone } from "../types/style.types";

// Body type mapping for interactive responses
const BODY_TYPE_MAPPING = {
  MALE: {
    "RECTANGLE": "RECTANGLE",
    "TRIANGLE": "TRIANGLE",
    "INVERTED_TRIANGLE": "INVERTED_TRIANGLE",
    "OVAL": "OVAL"
  },
  FEMALE: {
    "RECTANGLE": "RECTANGLE",
    "TRIANGLE": "PEAR",
    "INVERTED_TRIANGLE": "INVERTED_TRIANGLE",
    "OVAL": "APPLE",
    "HOURGLASS": "HOURGLASS"
  }
};

type BodyTypeCode = keyof typeof BODY_TYPE_MAPPING.MALE | keyof typeof BODY_TYPE_MAPPING.FEMALE;

export class StyleProfileUtils {
  private static userInfoDao = new UserInfoDao();

  /**
   * Processes interactive responses from users during style profile setup
   * Handles gender, body type, and undertone selections
   */
  static async processInteractiveResponse(
    userId: string,
    caseId: string,
    response: any,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService,
    styleProfileService: StyleProfileService
  ) {
    console.log(
      `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing interactive response:`,
      {
        userId,
        responseId: response.id,
        responseTitle: response.title,
      }
    );

    try {
      // Fetch current profile
      console.log(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Fetching user style profile for userId: ${userId}`
      );

      let currentProfile;
      try {
        currentProfile = await styleProfileService.getProfile(userId);
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Got profile:`,
          currentProfile
        );
      } catch (error) {
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} No existing profile found, will create new one if needed`,
          error
        );
        // Profile doesn't exist yet, that's okay
      }

      // Handle gender response
      if (["MALE", "FEMALE"].includes(response.id)) {
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing gender response:`,
          {
            userId,
            gender: response.id,
          }
        );

        // Create or update profile with gender
        if (!currentProfile) {
          // Create new profile with gender
          const createInput: CreateStyleProfileInput = {
            userId,
            userGender: response.id as Gender,
            userAge: "AGE_25_30", // Default value, will be updated later
            userUndertone: "COOL", // Default value, will be updated later
            userBodyType: "RECTANGLE", // Default value, will be updated later
            userHeight: "MEDIUM", // Default value, will be updated later
          };

          const createResponse = await styleProfileService.createProfile(createInput);
          console.log(
            `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Created new profile:`,
            createResponse
          );
        } else {
          // Update existing profile with gender
          const updateInput: UpdateStyleProfileInput = {
            userGender: response.id as Gender,
          };

          const updateResponse = await styleProfileService.updateProfile(userId, updateInput);
          console.log(
            `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with gender:`,
            updateResponse
          );
        }

        // Proceed to ask body type
        return await StyleProfileUtils.askBodyType(
          userId,
          caseId,
          response.id,
          messageId,
          messagePlatform,
          chatService
        );
      } 
      // Handle body type response
      else if (Object.keys(BODY_TYPE_MAPPING.MALE).includes(response.id) ||
               Object.keys(BODY_TYPE_MAPPING.FEMALE).includes(response.id)) {
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing body type response:`,
          {
            userId,
            bodyTypeCode: response.id,
          }
        );

        const gender = (currentProfile?.userGender || "MALE") as Gender;
        const bodyTypeCode = response.id as BodyTypeCode;
        const mappedBodyType = (BODY_TYPE_MAPPING as any)[gender][bodyTypeCode];

        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Mapped body type:`,
          {
            code: response.id,
            gender,
            mappedTo: mappedBodyType,
          }
        );

        // Update profile with mapped body type
        const updateInput: UpdateStyleProfileInput = {
          userBodyType: mappedBodyType as BodyType,
        };

        const updateResponse = await styleProfileService.updateProfile(userId, updateInput);
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with body type:`,
          updateResponse
        );

        // Proceed to ask skin tone/undertone
        return await StyleProfileUtils.askSkinTone(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      } 
      // Handle undertone response
      else if (["WARM", "COOL"].includes(response.id)) {
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing undertone response:`,
          {
            userId,
            undertone: response.id,
          }
        );

        // Update profile with undertone
        const updateInput: UpdateStyleProfileInput = {
          userUndertone: response.id as Undertone,
        };

        const updateResponse = await styleProfileService.updateProfile(userId, updateInput);
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with undertone:`,
          updateResponse
        );

        // Profile setup is now complete
        return {
          statusCode: 200,
          body: JSON.stringify({
            type: "PROFILE_SETUP_COMPLETE",
            message: "Style profile setup completed successfully!",
          }),
        };
      }

      // If we get here, it's an unrecognized response
      console.warn(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Unrecognized response:`,
        response
      );

      return {
        statusCode: 400,
        body: JSON.stringify({
          type: "UNRECOGNIZED_RESPONSE",
          message: "Unrecognized response type",
        }),
      };
    } catch (error) {
      console.error(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Error processing interactive response:`,
        {
          error,
          userId,
          responseId: response.id,
          errorMessage: error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  /**
   * Main handler for style profile setup
   * Determines the current state and next steps for profile completion
   */
  public static async handleStyleProfileSetup(
    caseId: string,
    caseData: any,
    userId: string,
    userData: any,
    messages: any[],
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService,
    styleProfileService: StyleProfileService
  ) {
    console.log(
      `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Starting style profile setup:`,
      {
        caseId,
        userId,
        messageCount: messages.length,
      }
    );

    try {
      // Check for interactive response first
      const lastMessage = messages[0]; // messages are in reverse order
      if (lastMessage?.messageType === "INTERACTIVE") {
        const messageContent = JSON.parse(lastMessage.messageContent);
        const response = messageContent.button_reply;

        if (response) {
          console.log(
            `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Processing interactive response:`,
            response
          );

          return await StyleProfileUtils.processInteractiveResponse(
            userId,
            caseId,
            response,
            messageId,
            messagePlatform,
            chatService,
            styleProfileService
          );
        }
      }

      // If no interactive response, check current profile state and continue setup
      let userStyleProfile;
      try {
        userStyleProfile = await styleProfileService.getProfile(userId);
        console.log(
          `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Retrieved user style profile:`,
          userStyleProfile
        );
      } catch (error) {
        console.log(
          `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} No existing profile found:`,
          error
        );
        // Profile doesn't exist yet, start from the beginning
      }

      // Determine next step based on current profile state
      if (!userStyleProfile) {
        // No profile exists, start with gender question
        return await StyleProfileUtils.askGender(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      } else if (!userStyleProfile.userBodyType || userStyleProfile.userBodyType === "RECTANGLE") {
        // Profile exists but body type not set, ask body type
        return await StyleProfileUtils.askBodyType(
          userId,
          caseId,
          userStyleProfile.userGender,
          messageId,
          messagePlatform,
          chatService
        );
      } else if (!userStyleProfile.userUndertone || userStyleProfile.userUndertone === "COOL") {
        // Profile exists but undertone not set, ask undertone
        return await StyleProfileUtils.askSkinTone(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      } else {
        // Profile is complete
        return {
          statusCode: 200,
          body: JSON.stringify({
            type: "PROFILE_SETUP_COMPLETE",
            message: "Style profile is already complete!",
          }),
        };
      }
    } catch (error) {
      console.error(
        `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Error in style profile setup:`,
        {
          error,
          userId,
          caseId,
          errorMessage: error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  // Helper methods for asking questions will be added in the next chunk
  private static async askGender(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    // Implementation will be added
    return {
      statusCode: 200,
      body: JSON.stringify({
        type: "GENDER_REQUESTED",
        message: "Please select your gender preference for styling",
      }),
    };
  }

  private static async askBodyType(
    userId: string,
    caseId: string,
    gender: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    // Implementation will be added
    return {
      statusCode: 200,
      body: JSON.stringify({
        type: "BODY_TYPE_REQUESTED",
        message: "Please select your body type",
      }),
    };
  }

  private static async askSkinTone(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    // Implementation will be added
    return {
      statusCode: 200,
      body: JSON.stringify({
        type: "SKIN_TONE_REQUESTED",
        message: "Please select your skin undertone",
      }),
    };
  }

  /**
   * Updates user style profile with provided data
   * Creates new profile if one doesn't exist
   */
  public static async updateUserStyleProfile(
    userId: string,
    updates: any,
    messageId: string,
    styleProfileService: StyleProfileService
  ) {
    console.log(
      `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Updating profile:`,
      {
        userId,
        updates,
      }
    );

    try {
      // Check if profile exists
      let profileExists = false;
      try {
        const existingProfile = await styleProfileService.getProfile(userId);
        profileExists = !!existingProfile;
      } catch (error) {
        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Profile doesn't exist, will create new one`
        );
      }

      if (!profileExists) {
        // Create new profile
        const createInput: CreateStyleProfileInput = {
          userId,
          userAge: "AGE_25_30", // Default value
          userGender: updates.userGender || "MALE",
          userUndertone: updates.userUndertone || "COOL",
          userBodyType: updates.userBodyType || "RECTANGLE",
          userHeight: updates.userHeight || "MEDIUM",
          ...updates,
        };

        const response = await styleProfileService.createProfile(createInput);
        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Created new profile:`,
          response
        );
      } else {
        // Update existing profile
        const updateInput: UpdateStyleProfileInput = {
          ...updates,
        };

        const response = await styleProfileService.updateProfile(userId, updateInput);
        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Updated existing profile:`,
          response
        );
      }

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Profile updated successfully",
        }),
      };
    } catch (error) {
      console.error(
        `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Error updating profile:`,
        {
          error,
          userId,
          updates,
          errorMessage: error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }
}
