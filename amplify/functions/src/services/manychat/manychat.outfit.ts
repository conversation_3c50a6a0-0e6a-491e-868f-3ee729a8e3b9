import { ManychatCommonService } from './manychat.common';
import { ManychatWeatherService } from './manychat.weather';
import { SetShoppableOutfitRequest, ManychatSetExistingItemsOutfitRequest, ManychatReviewMyOutfitRequest, ManychatReviewMyOutfitResponse } from '../../types/manychat';
import { ManychatFieldValue } from '../../external/manychat/types';
import { MANYCHAT_FIELD_IDS, MANYCHAT_STATUS } from '../../external/manychat/constants';
import { ApparelCategory, ApparelProfile, ApparelStatus } from '../../types/apparel.types';
import { AIService } from '../../ai/integrations/AIIntegration';
import { ApparelCollectionService } from '../apparelCollectionService';
import { TypeChatService } from '../../ai/integrations/TypeChatIntegration';

import { OutfitCurationUtil } from '../../utils/outfitCurationUtil';
import { ManychatStyleProfile } from '../../types/style.types';
import { OutfitError } from "../../types/outfits.types";
import { ManychatSituationalContext } from "../../types/manychat";
import { AIUtil } from '../../utils/aiUtil';
import { ImageCollageService } from '../imageCollageService';
import { OUTFIT_PARSING_SYSTEM_PROMPT, OUTFIT_PARSING_USER_PROMPT } from '../../ai/prompts/outfitParsingPrompt';
import { CLOTHING_TYPE_SYSTEM_PROMPT, CLOTHING_TYPE_USER_PROMPT } from '../../ai/prompts/clothingTypePrompt';
import {
  MaleTopwearType,
  MaleBottomwearType,
  MaleFootwearType,
  FemaleTopwearType,
  FemaleBottomwearType,
  FemaleFootwearType
} from '../../types/inventory.types';
import { ApparelService } from '../apparelService';
import { StyleProfileService } from '../styleProfileService';
import { ImageStorageService } from '../imageStorageService';
import { reviewOutfitPrompt } from '../../ai/prompts/reviewOutfitPrompt';
import { outfitReviewSchema, OutfitReviewResponse } from '../../ai/schemas/outfitReviewSchema';

// Interface for outfit parsing response
interface ProcessedItem {
  category: ApparelCategory;
  description: string;
}

interface OutfitParsingResponse {
  items: ProcessedItem[];
}

/**
 * Service for handling outfit operations in Manychat
 */
export class ManychatOutfitService {
  private typeChatService: TypeChatService;
  private outfitCurationUtil: OutfitCurationUtil;
  private imageCollageService: ImageCollageService;
  private manychatCommonService: ManychatCommonService;
  private apparelService: ApparelService;
  private styleProfileService: StyleProfileService;
  private manychatWeatherService: ManychatWeatherService;
  private imageStorageService: ImageStorageService;

  constructor() {
    this.typeChatService = new TypeChatService();
    this.outfitCurationUtil = new OutfitCurationUtil();
    this.imageCollageService = new ImageCollageService();
    this.manychatCommonService = new ManychatCommonService();
    this.apparelService = new ApparelService();
    this.styleProfileService = new StyleProfileService();
    this.manychatWeatherService = new ManychatWeatherService();
    this.imageStorageService = new ImageStorageService();
  }

  /**
   * Set a shoppable outfit in Manychat
   * @param request The request containing userId, outfit, and apparelProfile
   */
  async setShoppableOutfit(request: SetShoppableOutfitRequest): Promise<void> {
    try {
      console.log(`[ManychatOutfitService][setShoppableOutfit] Starting process for user ${request.userId}`, {
        outfit: request.outfit,
        apparelProfile: request.apparelProfile
      });

      // Process outfit into items
      const items = await this.processOutfitItems(request.outfit, request.userId);
      console.log(`[ManychatOutfitService][setShoppableOutfit] Processed outfit items for user ${request.userId}`, { items });

      // Limit to 4 items and log a warning if more than 4 items are found
      const limitedItems = items.slice(0, 4);
      if (items.length > 4) {
        console.warn(`[ManychatOutfitService][setShoppableOutfit] Warning: More than 4 items found for user ${request.userId}`, {
          totalItems: items.length,
          limitedItems: limitedItems.length,
          outfit: request.outfit
        });
      }

      // Search for products and collect results
      console.log(`[ManychatOutfitService][setShoppableOutfit] Starting product search for ${limitedItems.length} items`);
      const productResults = await Promise.all(
        limitedItems.map(item => this.searchProducts(item.description, item.category, request.apparelProfile))
      );

      // Filter out null results and prepare data for Manychat
      const validProducts = productResults.filter((result): result is NonNullable<typeof result> => result !== null);
      console.log(`[ManychatOutfitService][setShoppableOutfit] Product search results for user ${request.userId}`, {
        totalItems: limitedItems.length,
        validProducts: validProducts.length,
        products: validProducts.map(p => ({ name: p.productName, category: p.apparelCategory }))
      });

      if (validProducts.length === 0) {
        console.error(`[ManychatOutfitService][setShoppableOutfit] No valid products found for user ${request.userId}`, {
          outfit: request.outfit,
          items: limitedItems
        });
        throw new Error('No valid products found for the outfit');
      }

      // Create a collage of all product images
      let collageUrl: string | undefined;
      if (validProducts.length > 0) {
        try {
          const imageUrls = validProducts.map(product => product.imageUrl);
          collageUrl = await this.imageCollageService.createAndStoreCollage(imageUrls, request.userId);
          console.log(`[ManychatOutfitService][setShoppableOutfit] Created collage for user ${request.userId}`, { collageUrl });
        } catch (error) {
          console.error(`[ManychatOutfitService][setShoppableOutfit] Error creating collage for user ${request.userId}:`, error);
          // Continue without the collage if there's an error
        }
      }

      // Prepare custom fields for Manychat
      const customFields: Array<{ field_id: number; field_value: ManychatFieldValue }> = [
        {
          field_id: MANYCHAT_FIELD_IDS.NUMBER_OF_ITEMS,
          field_value: validProducts.length
        }
      ];

      // Add collage URL if available
      if (collageUrl) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.OUTFIT_COLLAGE_URL,
          field_value: collageUrl
        });
      }

      // Add fields for each valid product (up to 4) with direct Myntra URLs
      validProducts.forEach((product, index) => {
        const itemNumber = index + 1;
        const myntraUrl = `https://myntra.com/${product.productId}`;

        customFields.push(
          {
            field_id: MANYCHAT_FIELD_IDS[`ITEM${itemNumber}_CATEGORY` as keyof typeof MANYCHAT_FIELD_IDS],
            field_value: product.apparelCategory as string
          },
          {
            field_id: MANYCHAT_FIELD_IDS[`ITEM${itemNumber}_IMAGE_URL` as keyof typeof MANYCHAT_FIELD_IDS],
            field_value: product.imageUrl as string
          },
          {
            field_id: MANYCHAT_FIELD_IDS[`ITEM${itemNumber}_PRODUCT_NAME` as keyof typeof MANYCHAT_FIELD_IDS],
            field_value: product.productName as string
          },
          {
            field_id: MANYCHAT_FIELD_IDS[`ITEM${itemNumber}_PRODUCT_URL` as keyof typeof MANYCHAT_FIELD_IDS],
            field_value: myntraUrl
          }
        );
      });

      console.log(`[ManychatOutfitService][setShoppableOutfit] Updating Manychat fields for user ${request.userId}`, {
        numberOfFields: customFields.length,
        fields: customFields.map(f => ({ fieldId: f.field_id }))
      });

      // Update Manychat fields
      await this.manychatCommonService.setCustomFields(Number(request.userId), customFields);

      console.log(`[ManychatOutfitService][setShoppableOutfit] Successfully completed for user ${request.userId}`);

    } catch (error) {
      console.error('[ManychatOutfitService][setShoppableOutfit] Error processing request:', {
        userId: request.userId,
        outfit: request.outfit,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * Set an outfit using existing items from user's wardrobe
   * @param request The request containing userId, manychatSubscriberId, and situationContext
   */
  async setExistingItemsOutfit(request: ManychatSetExistingItemsOutfitRequest): Promise<void> {
    try {
      const { userId, manychatSubscriberId, situationContext } = request;

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Starting process for user ${userId}`, {
        situationContext: JSON.stringify(situationContext)
      });

      // Get user's style profile
      const styleProfile = await this.styleProfileService.getProfile(userId);
      if (!styleProfile) {
        throw new Error(`Style profile not found for user ${userId}`);
      }

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Retrieved style profile for user ${userId}`);

      // Process location and weather data according to the requirements
      let updatedSituationContext = { ...situationContext };
      let cityName: string | undefined;

      // Case 1: If we get both location and weather, use them directly
      if (situationContext.location && situationContext.feelsLikeWeather) {
        console.log(`[ManychatOutfitService][setExistingItemsOutfit] Using provided location and weather for user ${userId}`, {
          location: situationContext.location,
          weather: situationContext.feelsLikeWeather
        });

        // Check if location is in coordinates format and convert to city name
        const coordinatesMatch = situationContext.location.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);
        if (coordinatesMatch) {
          try {
            console.log(`[ManychatOutfitService][setExistingItemsOutfit] Location appears to be coordinates, getting city name`);
            const cityData = await this.manychatWeatherService.getCityFromCoordinatesString(situationContext.location);
            cityName = cityData.cityName;
            console.log(`[ManychatOutfitService][setExistingItemsOutfit] Retrieved city name: ${cityName} for coordinates: ${situationContext.location}`);
          } catch (error) {
            console.error(`[ManychatOutfitService][setExistingItemsOutfit] Error getting city name for coordinates: ${situationContext.location}`, {
              error: error instanceof Error ? error.message : 'Unknown error'
            });
            // Continue with the original location if there's an error
          }
        } else {
          // If not coordinates, use the location as is for city name
          cityName = situationContext.location;
        }
      }
      // Case 2: If we get only location, call weather API to get the weather
      else if (situationContext.location && !situationContext.feelsLikeWeather) {
        try {
          console.log(`[ManychatOutfitService][setExistingItemsOutfit] Getting weather for location: ${situationContext.location}`);
          const weatherData = await this.manychatWeatherService.getWeatherForLocation(situationContext.location);
          updatedSituationContext.feelsLikeWeather = weatherData.description;

          // Use the location from the weather response which includes the city name
          cityName = weatherData.location.split(',')[0].trim(); // Extract just the city name part

          console.log(`[ManychatOutfitService][setExistingItemsOutfit] Retrieved weather for location: ${situationContext.location}`, {
            weather: weatherData.description,
            cityName: cityName
          });
        } catch (error) {
          console.error(`[ManychatOutfitService][setExistingItemsOutfit] Error getting weather for location: ${situationContext.location}`, {
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          // Continue without weather data if there's an error
        }
      }
      // Case 3: If we get only weather, use it as is
      else if (!situationContext.location && situationContext.feelsLikeWeather) {
        console.log(`[ManychatOutfitService][setExistingItemsOutfit] Using provided weather for user ${userId}`, {
          weather: situationContext.feelsLikeWeather
        });
      }
      // Case 4: If we get neither, just proceed without location and weather
      else if (!situationContext.location && !situationContext.feelsLikeWeather) {
        console.log(`[ManychatOutfitService][setExistingItemsOutfit] No location or weather provided for user ${userId}`);
      }

      // Get user's apparels (non-archived)
      const { items: userApparels } = await this.apparelService.listApparelsWithFilters(
        userId,
        { apparelStatus: ApparelStatus.PRIMARY }
      );

      if (!userApparels || userApparels.length === 0) {
        throw new Error(`No active apparels found in user's wardrobe for user ${userId}`);
      }

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Retrieved ${userApparels.length} apparels for user ${userId}`);

      // Validate that we have enough apparels to create an outfit
      try {
        this.outfitCurationUtil.validateEligibilityForOutfitCuration(
          userApparels.map(item => item.apparel)
        );
      } catch (validationError) {
        console.error(`[ManychatOutfitService][setExistingItemsOutfit] Validation failed for user ${userId}:`, {
          error: validationError instanceof Error ? validationError.message : 'Unknown error',
          apparelCount: userApparels.length
        });
        throw validationError;
      }

      // Call the Manychat-specific outfit curation utility to generate an outfit from existing apparels
      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Sending apparels to outfit curation:`,
        userApparels.map(item => ({
          id: item.apparel.apparelId,
          type: item.apparel.apparelType,
          category: item.apparel.apparelCategory
        }))
      );
      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Sending apparels to outfit curation with context:`, {
        apparels: userApparels.map(item => item.apparel.apparelId),
        context: updatedSituationContext
      });
      const outfitResult = await this.outfitCurationUtil.processOutfitCurationFromExistingApparelsManychat(
        userId,
        updatedSituationContext,
        userApparels.map(item => item.apparel)
      );

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Received outfit result:`, {
        outfitName: outfitResult.outfitName,
        apparelPointers: outfitResult.apparelPointers,
        apparelItemsCount: outfitResult.apparelItems ? outfitResult.apparelItems.length : 0
      });

      if (outfitResult.apparelItems) {
        console.log(`[ManychatOutfitService][setExistingItemsOutfit] Outfit result apparel items:`,
          outfitResult.apparelItems.map(item => ({
            id: item.apparelId,
            type: item.apparelType,
            category: item.apparelCategory
          }))
        );
      }

      if (!outfitResult || outfitResult.apparelPointers.length === 0) {
        throw new Error(`Failed to generate outfit for user ${userId}`);
      }

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Generated outfit for user ${userId}`, {
        outfitName: outfitResult.outfitName,
        apparelCount: outfitResult.apparelPointers.length
      });

      // Get the apparel details from the userApparels we already fetched
      const validApparels = userApparels
        .map(item => item.apparel)
        .filter(apparel => outfitResult.apparelPointers.includes(apparel.apparelId || ''));

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Retrieved ${validApparels.length} valid apparels for outfit`);

      // Create a collage of all apparel images
      let collageUrl: string | undefined;
      if (validApparels.length > 0) {
        try {
          const imageUrls = validApparels.map(apparel => apparel!.apparelMediaUrl);
          collageUrl = await this.imageCollageService.createAndStoreCollage(imageUrls, userId);
          console.log(`[ManychatOutfitService][setExistingItemsOutfit] Created collage for user ${userId}`, { collageUrl });
        } catch (error) {
          console.error(`[ManychatOutfitService][setExistingItemsOutfit] Error creating collage for user ${userId}:`, error);
          // Continue without the collage if there's an error
        }
      }

      // Prepare custom fields for Manychat
      const customFields: Array<{ field_id: number; field_value: ManychatFieldValue }> = [
        {
          field_id: MANYCHAT_FIELD_IDS.NUMBER_OF_ITEMS,
          field_value: validApparels.length
        },
        {
          field_id: MANYCHAT_FIELD_IDS.OUTFIT_CURATION_STATUS,
          field_value: MANYCHAT_STATUS.SUCCESS
        }
      ];

      // Add user location if available - use city name if we have it, otherwise use the original location
      if (cityName) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.USER_LOCATION,
          field_value: cityName
        });
      } else if (updatedSituationContext.location) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.USER_LOCATION,
          field_value: updatedSituationContext.location
        });
      }

      // Add outfit name to Manychat fields
      if (outfitResult.outfitName) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.OUTFIT_NAME,
          field_value: outfitResult.outfitName
        });
      }

      // Add collage URL if available
      if (collageUrl) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.OUTFIT_COLLAGE_URL,
          field_value: collageUrl
        });
      }

      // Add fields for each valid apparel (up to 4)
      validApparels.slice(0, 4).forEach((apparel, index) => {
        const itemNumber = index + 1;

        customFields.push(
          {
            field_id: MANYCHAT_FIELD_IDS[`ITEM${itemNumber}_CATEGORY` as keyof typeof MANYCHAT_FIELD_IDS],
            field_value: apparel!.apparelCategory as string
          },
          {
            field_id: MANYCHAT_FIELD_IDS[`ITEM${itemNumber}_PRODUCT_NAME` as keyof typeof MANYCHAT_FIELD_IDS],
            field_value: apparel!.productName || `${apparel!.apparelType} (${apparel!.apparelCategory})`
          }
        );
      });

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Updating Manychat fields for user ${userId}`, {
        numberOfFields: customFields.length,
        fields: customFields.map(f => ({ fieldId: f.field_id, value: typeof f.field_value === 'string' && f.field_value.length > 50 ? `${f.field_value.substring(0, 50)}...` : f.field_value }))
      });

      // Update Manychat fields
      await this.manychatCommonService.setCustomFields(manychatSubscriberId, customFields);

      console.log(`[ManychatOutfitService][setExistingItemsOutfit] Successfully completed for user ${userId}`);
    } catch (error) {
      console.error('[ManychatOutfitService][setExistingItemsOutfit] Error processing request:', {
        userId: request.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCode: error instanceof OutfitError ? error.errorCode : undefined,
        stack: error instanceof Error ? error.stack : undefined
      });

      // Set error status in Manychat fields
      try {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.log(`[ManychatOutfitService][setExistingItemsOutfit] Setting error status in Manychat for user ${request.userId}`);

        const errorFields = [
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_CURATION_STATUS,
            field_value: MANYCHAT_STATUS.FAILURE
          },
          {
            field_id: MANYCHAT_FIELD_IDS.OUTFIT_CURATION_ERROR_MESSAGE,
            field_value: errorMessage
          }
        ];

        await this.manychatCommonService.setCustomFields(request.manychatSubscriberId, errorFields);
        console.log(`[ManychatOutfitService][setExistingItemsOutfit] Successfully set error status in Manychat for user ${request.userId}`);
      } catch (manychatError) {
        console.error(`[ManychatOutfitService][setExistingItemsOutfit] Failed to set error status in Manychat for user ${request.userId}:`, {
          originalError: error instanceof Error ? error.message : 'Unknown error',
          manychatError: manychatError instanceof Error ? manychatError.message : 'Unknown error'
        });
      }

      // Re-throw the original error
      throw error;
    }
  }

  /**
   * Get a GenAI-generated outfit based on style profile and situational context
   * @param styleProfile The user's style profile
   * @param context The situational context for the outfit
   * @param userId The user ID
   */
  async setGenAIOutfit(
    styleProfile: ManychatStyleProfile,
    context: ManychatSituationalContext,
    userId: string
  ): Promise<void> {
    try {
      console.log(`[ManychatOutfitService][setGenAIOutfit] Starting outfit generation`, {
        styleProfile: JSON.stringify(styleProfile),
        context: JSON.stringify(context)
      });

      // Initialize utilities
      const aiUtil = new AIUtil();

      // Process location and weather data according to the requirements
      let updatedContext = { ...context };
      let cityName: string | undefined;

      // Case 1: If we get both location and weather, use them directly
      if (context.location && context.feelsLikeWeather) {
        console.log(`[ManychatOutfitService][setGenAIOutfit] Using provided location and weather for user ${userId}`, {
          location: context.location,
          weather: context.feelsLikeWeather
        });

        // Check if location is in coordinates format and convert to city name
        const coordinatesMatch = context.location.match(/(-?\d+\.\d+),\s*(-?\d+\.\d+)/);
        if (coordinatesMatch) {
          try {
            console.log(`[ManychatOutfitService][setGenAIOutfit] Location appears to be coordinates, getting city name`);
            const cityData = await this.manychatWeatherService.getCityFromCoordinatesString(context.location);
            cityName = cityData.cityName;
            console.log(`[ManychatOutfitService][setGenAIOutfit] Retrieved city name: ${cityName} for coordinates: ${context.location}`);
          } catch (error) {
            console.error(`[ManychatOutfitService][setGenAIOutfit] Error getting city name for coordinates: ${context.location}`, {
              error: error instanceof Error ? error.message : 'Unknown error'
            });
            // Continue with the original location if there's an error
          }
        } else {
          // If not coordinates, use the location as is for city name
          cityName = context.location;
        }
      }
      // Case 2: If we get only location, call weather API to get the weather
      else if (context.location && !context.feelsLikeWeather) {
        try {
          console.log(`[ManychatOutfitService][setGenAIOutfit] Getting weather for location: ${context.location}`);
          const weatherData = await this.manychatWeatherService.getWeatherForLocation(context.location);
          updatedContext.feelsLikeWeather = weatherData.description;

          // Use the location from the weather response which includes the city name
          cityName = weatherData.location.split(',')[0].trim(); // Extract just the city name part

          console.log(`[ManychatOutfitService][setGenAIOutfit] Retrieved weather for location: ${context.location}`, {
            weather: weatherData.description,
            cityName: cityName
          });
        } catch (error) {
          console.error(`[ManychatOutfitService][setGenAIOutfit] Error getting weather for location: ${context.location}`, {
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          // Continue without weather data if there's an error
        }
      }
      // Case 3: If we get only weather, use it as is
      else if (!context.location && context.feelsLikeWeather) {
        console.log(`[ManychatOutfitService][setGenAIOutfit] Using provided weather for user ${userId}`, {
          weather: context.feelsLikeWeather
        });
      }
      // Case 4: If we get neither, just proceed without location and weather
      else if (!context.location && !context.feelsLikeWeather) {
        console.log(`[ManychatOutfitService][setGenAIOutfit] No location or weather provided for user ${userId}`);
      }

      // Call the outfit curation utility to generate an outfit with the updated context
      console.log(`[ManychatOutfitService][setGenAIOutfit] Calling outfit curation with context:`, {
        context: updatedContext
      });
      const outfitResponse = await this.outfitCurationUtil.processOutfitCurationGenAIManychat(
        styleProfile,
        updatedContext
      );

      console.log(`[ManychatOutfitService][setGenAIOutfit] Generated outfit response`, {
        response: outfitResponse
      });

      // Clean up the response to ensure it's a simple string
      let outfitString = outfitResponse;

      // Remove any markdown code blocks or other formatting
      outfitString = this.outfitCurationUtil.jsonToPlainString(outfitString);                          // Remove extra whitespace

      // Emojify the outfit description using the cleaned string
      const emojifiedOutfit = await aiUtil.emojifyOutfit(
        context.occasion,
        outfitString
      );

      console.log(`[ManychatOutfitService][setGenAIOutfit] Emojified outfit`, { emojifiedOutfit });

      // Set fields in Manychat
      const customFields: Array<{ field_id: number; field_value: ManychatFieldValue }> = [
        {
          field_id: MANYCHAT_FIELD_IDS.PLAIN_GPT_RESPONSE,
          field_value: outfitString
        },
        {
          field_id: MANYCHAT_FIELD_IDS.TEMP_GPT_RESPONSE,
          field_value: emojifiedOutfit
        }
      ];

      // Add user location if available - use city name if we have it, otherwise use the original location
      if (cityName) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.USER_LOCATION,
          field_value: cityName
        });
      } else if (updatedContext.location) {
        customFields.push({
          field_id: MANYCHAT_FIELD_IDS.USER_LOCATION,
          field_value: updatedContext.location
        });
      }

      // Update Manychat fields
      await this.manychatCommonService.setCustomFields(Number(userId), customFields);

      console.log(`[ManychatOutfitService][setGenAIOutfit] Successfully set outfit in Manychat for user ${userId}`);

    } catch (error) {
      console.error(`[ManychatOutfitService][setGenAIOutfit] Error generating outfit:`, error);
      throw error;
    }
  }

  /**
   * Process outfit string into individual items using AI
   * @private
   */
  private async processOutfitItems(outfit: string, userId: string): Promise<ProcessedItem[]> {
    console.log(`[ManychatOutfitService][processOutfitItems] Starting outfit processing for user ${userId}`, { outfit });
    const aiService = new AIService();

    try {
      console.log(`[ManychatOutfitService][processOutfitItems] Sending request to AI service for user ${userId}`);
      // Get AI response
      const aiResponse = await aiService.getAIResponse([OUTFIT_PARSING_USER_PROMPT(outfit)], OUTFIT_PARSING_SYSTEM_PROMPT);
      console.log(`[ManychatOutfitService][processOutfitItems] Received AI response for user ${userId}`, { aiResponse });

      // Try direct JSON parsing first
      try {
        // Clean the response to ensure it's valid JSON
        const cleanedResponse = aiResponse.replace(/```json\n|\n```/g, '').trim();
        const response = JSON.parse(cleanedResponse) as OutfitParsingResponse;
        console.log(`[ManychatOutfitService][processOutfitItems] Successfully parsed AI response for user ${userId}`, { items: response.items });
        return response.items;
      } catch (parseError) {
        console.log('[ManychatOutfitService][processOutfitItems] Direct JSON parsing failed, trying TypeChat:', {
          userId,
          outfit,
          error: parseError instanceof Error ? parseError.message : 'Unknown parse error',
          rawResponse: aiResponse
        });

        // Fallback to TypeChat parsing
        const response = await this.typeChatService.parseOutfitParsingResponse(aiResponse);
        console.log(`[ManychatOutfitService][processOutfitItems] Successfully parsed response using TypeChat for user ${userId}`, { items: response.items });
        return response.items;
      }
    } catch (error) {
      console.error('[ManychatOutfitService][processOutfitItems] Failed to process outfit:', {
        userId,
        outfit,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to process outfit description');
    }
  }

  /**
   * Search for products in Elasticsearch
   * @private
   */
  private async searchProducts(item: string, category: ApparelCategory, apparelProfile: ApparelProfile): Promise<{
    productName: string;
    productId: string;
    imageUrl: string;
    apparelCategory: string;
  } | null> {
    console.log(`[ManychatOutfitService][searchProducts] Starting product search`, { item, category, apparelProfile });
    const apparelCollectionService = new ApparelCollectionService();
    const aiService = new AIService();

    try {
      // Determine the valid clothing types based on apparelProfile and category
      let validTypes: string[];
      if (apparelProfile === "MALE") {
        if (category === "TOPWEAR") {
          validTypes = Object.values(MaleTopwearType);
        } else if (category === "BOTTOMWEAR") {
          validTypes = Object.values(MaleBottomwearType);
        } else {
          validTypes = Object.values(MaleFootwearType);
        }
      } else {
        if (category === "TOPWEAR") {
          validTypes = Object.values(FemaleTopwearType);
        } else if (category === "BOTTOMWEAR") {
          validTypes = Object.values(FemaleBottomwearType);
        } else {
          validTypes = Object.values(FemaleFootwearType);
        }
      }

      // Get clothing type from AI
      console.log(`[ManychatOutfitService][searchProducts] Determining clothing type using AI`);
      const aiResponse = await aiService.getAIResponse(
        [CLOTHING_TYPE_USER_PROMPT(item, validTypes)],
        CLOTHING_TYPE_SYSTEM_PROMPT
      );

      // Clean and parse the AI response
      const clothingType = aiResponse.trim().toLowerCase();
      console.log(`[ManychatOutfitService][searchProducts] Determined clothing type: ${clothingType}`);

      // Search for the item in Elasticsearch with category and type filter
      console.log(`[ManychatOutfitService][searchProducts] Searching Elasticsearch for item: ${item} in category: ${category} and type: ${clothingType}`);
      const searchResult = await apparelCollectionService.searchManychatApparels(
        item,
        1,
        10,
        apparelProfile,
        category,
        clothingType
      );

      // Check if we found any results
      if (!searchResult.results || searchResult.results.length === 0) {
        console.log(`[ManychatOutfitService][searchProducts] No products found for item: ${item} in category: ${category}`);
        return null;
      }

      // Get a random product from the results
      const randomIndex = Math.floor(Math.random() * searchResult.results.length);
      const product = searchResult.results[randomIndex];
      console.log(`[ManychatOutfitService][searchProducts] Selected random product from ${searchResult.results.length} results`, {
        productName: product.productName,
        productId: product.productId,
        category: product.apparelCategory
      });

      // Extract required fields from the product
      return {
        productName: product.productName,
        productId: product.productId.toString(),
        imageUrl: product.hero_image_url,
        apparelCategory: product.apparelCategory
      };
    } catch (error) {
      console.error(`[ManychatOutfitService][searchProducts] Error searching for product: ${item} in category: ${category}`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        apparelProfile
      });
      return null;
    }
  }

  /**
   * Review a user's outfit using AI analysis
   * @param request The request containing userId, manychatSubscriberId, imageURL, and imageCaption
   * @returns Promise<ManychatReviewMyOutfitResponse> The AI-generated outfit review, tailored for Manychat
   */
  async reviewMyOutfit(request: ManychatReviewMyOutfitRequest): Promise<ManychatReviewMyOutfitResponse> {
    try {
      const { userId, manychatSubscriberId, imageURL, imageCaption } = request;

      console.log(`[ManychatOutfitService][reviewMyOutfit] Starting outfit review for user ${userId}`, {
        imageCaption,
        imageUrl: imageURL.substring(0, 30) + '...'
      });

      // Save the image to Azure storage with organized path
      const storagePath = `users/${userId}/reviewedOutfits/outfit-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
      const storedImageUrl = await this.imageStorageService.uploadImage(imageURL, storagePath);

      console.log(`[ManychatOutfitService][reviewMyOutfit] Image saved to storage for user ${userId}`, {
        storagePath,
        storedUrl: storedImageUrl.substring(0, 30) + '...'
      });

      // Get user's style profile
      const styleProfile = await this.styleProfileService.getProfile(userId);
      if (!styleProfile) {
        throw new Error(`Style profile not found for user ${userId}`);
      }

      console.log(`[ManychatOutfitService][reviewMyOutfit] Retrieved style profile for user ${userId}`);

      // Get user's wardrobe (non-archived)
      const { items: userApparels } = await this.apparelService.listApparelsWithFilters(
        userId,
        { apparelStatus: ApparelStatus.PRIMARY }
      );

      console.log(`[ManychatOutfitService][reviewMyOutfit] Retrieved ${userApparels?.length || 0} apparels for user ${userId}`);

      // Prepare the user image info
      const userImageInfo = {
        imageUrl: storedImageUrl,
        imageCaption: imageCaption
      };

      // Prepare the style profile info for the AI - only include fields that have values
      const styleProfileInfo: any = {
        userId: styleProfile.userId,
        gender: styleProfile.userGender,
        undertone: styleProfile.userUndertone,
        bodyType: styleProfile.userBodyType,
        height: styleProfile.userHeight
      };

      // Only add optional fields if they exist
      if (styleProfile.userSeason) styleProfileInfo.season = styleProfile.userSeason;
      if (styleProfile.userSkinTone) styleProfileInfo.complexion = styleProfile.userSkinTone;
      if (styleProfile.userContrast) styleProfileInfo.coloring = styleProfile.userContrast;
      if (styleProfile.userEyeColor) styleProfileInfo.eyeColor = styleProfile.userEyeColor;
      if (styleProfile.userHairColor) styleProfileInfo.hairColor = styleProfile.userHairColor;

      // Log the style profile being sent to the prompt
      console.log(`[ManychatOutfitService][reviewMyOutfit] Style profile for AI prompt for user ${userId}:`, JSON.stringify(styleProfileInfo, null, 2));

      // Prepare the user wardrobe data - send the actual apparel objects
      const userWardrobe = {
        Apparels: userApparels?.map(item => item.apparel) || []
      };
      // Log the entire wardrobe
      console.log(`[ManychatOutfitService][reviewMyOutfit] User wardrobe for user ${userId}:`, JSON.stringify(userWardrobe, null, 2));
      // Prepare the full prompt with all the data
      const fullPrompt = `
${reviewOutfitPrompt}

**USER IMAGE INFO:**
${JSON.stringify(userImageInfo, null, 2)}

**FIXED USER PARAMETERS:**
${JSON.stringify({ StyleProfileInfo: styleProfileInfo }, null, 2)}

**USER WARDROBE:**
${JSON.stringify(userWardrobe, null, 2)}
`;

      console.log(`[ManychatOutfitService][reviewMyOutfit] Calling AI service for outfit review`, {
        userId,
        promptLength: fullPrompt.length,
        wardrobeItemsCount: userWardrobe.Apparels.length
      });

      // Initialize AI service
      const aiService = new AIService();

      // Call Gemini with structured output
      const aiReviewResult = await aiService.getGeminiImageStructuredOutput<OutfitReviewResponse>(
        storedImageUrl,
        fullPrompt,
        "You are Monova, an expert AI Fashion Stylist providing comprehensive outfit reviews.",
        outfitReviewSchema,
        0.3
      );

      console.log(`[ManychatOutfitService][reviewMyOutfit] Successfully generated outfit review for user ${userId}`, {
        reviewFeedbackLength: aiReviewResult.reviewFeedback?.length || 0,
        wardrobeAlternativesCount: aiReviewResult.suggestedUserWardrobeAlternatives?.length || 0,
        marketplaceAlternativesCount: aiReviewResult.suggestedMarketplaceAlternatives?.length || 0
      });

      let wardrobeCollageUrl: string | undefined;
      // Process suggested user wardrobe alternatives into a collage
      if (aiReviewResult.suggestedUserWardrobeAlternatives && aiReviewResult.suggestedUserWardrobeAlternatives.length > 0) {
        console.log(`[ManychatOutfitService][reviewMyOutfit] Processing suggested user wardrobe alternatives for collage`, {
          userId,
          suggestedIds: aiReviewResult.suggestedUserWardrobeAlternatives
        });
        // Find the corresponding apparel objects from the user's wardrobe
        const suggestedApparels = userApparels
          ?.map(item => item.apparel)
          .filter(apparel => apparel && apparel.apparelId && aiReviewResult.suggestedUserWardrobeAlternatives!.includes(apparel.apparelId));

        if (suggestedApparels && suggestedApparels.length > 0) {
          const imageUrls = suggestedApparels
            .map(apparel => apparel.apparelMediaUrl)
            .filter((url): url is string => !!url); // Ensure only valid string URLs are passed

          if (imageUrls.length > 0) {
            try {
              console.log(`[ManychatOutfitService][reviewMyOutfit] Creating collage for ${imageUrls.length} suggested wardrobe items`);
              wardrobeCollageUrl = await this.imageCollageService.createAndStoreCollage(imageUrls, userId);
              console.log(`[ManychatOutfitService][reviewMyOutfit] Created wardrobe suggestions collage for user ${userId}`, {
                collageUrl: wardrobeCollageUrl ? wardrobeCollageUrl.substring(0, 30) + '...' : 'N/A'
              });
            } catch (collageError) {
              console.error(`[ManychatOutfitService][reviewMyOutfit] Error creating wardrobe suggestions collage for user ${userId}:`, collageError);
              // Continue without the collage if there's an error
            }
          } else {
             console.warn(`[ManychatOutfitService][reviewMyOutfit] No valid image URLs found for suggested wardrobe items for user ${userId}`);
          }
        } else {
           console.warn(`[ManychatOutfitService][reviewMyOutfit] No matching apparels found in user's wardrobe for suggested IDs for user ${userId}`);
        }
      } else {
        console.log(`[ManychatOutfitService][reviewMyOutfit] No user wardrobe alternatives suggested by AI for user ${userId}`);
      }

      // Convert marketplace suggestions array to string for Manychat compatibility
      let marketplaceAlternativesString: string | undefined;
      if (aiReviewResult.suggestedMarketplaceAlternatives && aiReviewResult.suggestedMarketplaceAlternatives.length > 0) {
        const marketplaceAlternativesArray = aiReviewResult.suggestedMarketplaceAlternatives.map(item => ({
          ...item,
          apparelProfile: item.apparelProfile as ApparelProfile,
        }));

        // Convert array to JSON string and then clean it using jsonToPlainString
        const marketplaceAlternativesJson = JSON.stringify(marketplaceAlternativesArray);
        marketplaceAlternativesString = this.outfitCurationUtil.jsonToPlainString(marketplaceAlternativesJson);

        console.log(`[ManychatOutfitService][reviewMyOutfit] Converted marketplace alternatives to string for user ${userId}`, {
          originalCount: aiReviewResult.suggestedMarketplaceAlternatives.length,
          stringLength: marketplaceAlternativesString.length
        });
      }

      // Prepare the final response object, tailored for Manychat
      const finalResponse: ManychatReviewMyOutfitResponse = {
        reviewFeedback: aiReviewResult.reviewFeedback,
        suggestedMarketplaceAlternativesString: marketplaceAlternativesString,
        userWardrobeSuggestionsCollage: wardrobeCollageUrl,
        // suggestedUserWardrobeAlternatives (array of IDs) is intentionally omitted here
      };

      console.log(`[ManychatOutfitService][reviewMyOutfit] Final outfit review response prepared for user ${userId}`, {
        reviewFeedbackLength: finalResponse.reviewFeedback?.length || 0,
        wardrobeCollageUrl: finalResponse.userWardrobeSuggestionsCollage ? finalResponse.userWardrobeSuggestionsCollage.substring(0, 30) + '...' : 'N/A',
        marketplaceAlternativesStringLength: finalResponse.suggestedMarketplaceAlternativesString?.length || 0
      });

      return finalResponse;

    } catch (error) {
      console.error('[ManychatOutfitService][reviewMyOutfit] Error processing outfit review:', {
        userId: request.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }
}
