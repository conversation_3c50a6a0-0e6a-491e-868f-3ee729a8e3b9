import { MessageOrigin, MessagePlatform, CaseStatus, UserStatus } from '../config/enums';
import { MessageDao } from '../db/messageDao';
import { UserService } from "./userService";
import { CaseService } from "./caseService";

export class GatewayService {
  private messageDao: MessageDao;
  private userService: UserService;
  private caseService: CaseService;

  constructor() {
    console.log('[GatewayService] Initializing GatewayService...');
    this.messageDao = new MessageDao();
    this.userService = new UserService();
    this.caseService = new CaseService();
  }

  /**
   * Get or create user
   */
  async getOrCreateUser(
    userId: string,
    userFullName: string,
    userWhatsAppNumber: string,
    messageId: string
  ) {
    console.log(
      `[GatewayService][getOrCreateUser] tid=${messageId} Getting or creating user:`,
      { userId, userFullName, userWhatsAppNumber }
    );

    try {
      // Try to get existing user
      let user: any = await this.userService.getUserById(userId);

      if (!user) {
        console.log(
          `[GatewayService][getOrCreateUser] tid=${messageId} User not found, creating new user`
        );

        // Create new user
        user = await this.userService.createUser({
          userId,
          userFullName,
          userWhatsAppNumber,
        });
      }

      console.log(
        `[GatewayService][getOrCreateUser] tid=${messageId} User retrieved/created successfully`
      );

      return user;
    } catch (error) {
      console.error(
        `[GatewayService][getOrCreateUser] tid=${messageId} Error:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get active case for user
   */
  async getActiveCase(userId: string, messageId: string) {
    console.log(
      `[GatewayService][getActiveCase] tid=${messageId} Getting active case for user:`,
      { userId }
    );

    try {
      // Get all cases for user and find the most recent active one
      const casesResult = await this.caseService.listCases({
        userId,
        limit: 10, // Get recent cases
      });

      const cases = casesResult.items || [];

      // Find the most recent case that's not closed
      const activeCase = cases.find((caseItem: any) =>
        caseItem.caseStatus !== CaseStatus.CLOSED
      );

      if (activeCase) {
        console.log(
          `[GatewayService][getActiveCase] tid=${messageId} Found active case:`,
          { caseId: activeCase.caseId, caseStatus: activeCase.caseStatus }
        );
      } else {
        console.log(
          `[GatewayService][getActiveCase] tid=${messageId} No active case found`
        );
      }

      return activeCase || null;
    } catch (error) {
      console.error(
        `[GatewayService][getActiveCase] tid=${messageId} Error:`,
        error
      );
      throw error;
    }
  }

  /**
   * Create new case
   */
  async createNewCase(userId: string, messageId: string) {
    console.log(
      `[GatewayService][createNewCase] tid=${messageId} Creating new case for user:`,
      { userId }
    );

    try {
      const newCase = await this.caseService.createCase({
        userId,
        caseStatus: CaseStatus.NEW,
        messages: [],
      });

      console.log(
        `[GatewayService][createNewCase] tid=${messageId} Case created successfully:`,
        { caseId: newCase.caseId }
      );

      return newCase;
    } catch (error) {
      console.error(
        `[GatewayService][createNewCase] tid=${messageId} Error:`,
        error
      );
      throw error;
    }
  }

  /**
   * Store message
   */
  async storeMessage(messageData: {
    caseId: string;
    userId: string;
    messageId: string;
    messageType: string;
    messageDirection: string;
    messageContent: any;
    messagePlatform: MessagePlatform;
    messageOrigin: MessageOrigin;
    messageContext?: any;
  }) {
    console.log(
      `[GatewayService][storeMessage] tid=${messageData.messageId} Storing message:`,
      {
        caseId: messageData.caseId,
        userId: messageData.userId,
        messageType: messageData.messageType,
        messageDirection: messageData.messageDirection,
        messagePlatform: messageData.messagePlatform,
        messageOrigin: messageData.messageOrigin,
      }
    );

    try {
      const storedMessageId = await this.messageDao.createMessage(
        messageData.caseId,
        messageData.messageId,
        messageData.userId,
        messageData.messageDirection as "INBOUND" | "OUTBOUND",
        "RECEIVED",
        messageData.messageType as "TEXT" | "IMAGE" | "INTERACTIVE" | "LOG",
        messageData.messageContent,
        messageData.messagePlatform,
        messageData.messageOrigin,
        messageData.messageContext
      );

      // Add message to case
      await this.caseService.addMessageToCase(
        messageData.userId,
        messageData.caseId,
        messageData.messageId
      );

      console.log(
        `[GatewayService][storeMessage] tid=${messageData.messageId} Message stored successfully`
      );

      return {
        messageId: storedMessageId,
        caseId: messageData.caseId,
        userId: messageData.userId,
        messageType: messageData.messageType,
        messageDirection: messageData.messageDirection,
        messageContent: messageData.messageContent,
        messagePlatform: messageData.messagePlatform,
        messageOrigin: messageData.messageOrigin,
        messageContext: messageData.messageContext,
        createdAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error(
        `[GatewayService][storeMessage] tid=${messageData.messageId} Error:`,
        error
      );
      throw error;
    }
  }
}
