import { UserStatus } from "../config/enums";
import { UserInfoDao } from "../db/userInfoDao";
import { v4 as uuidv4 } from "uuid";

interface CreateUserInput {
  userId: string;
  userFullName: string;
  userWhatsAppNumber: string;
}

interface UpdateUserInput {
  userFullName?: string;
  userWhatsAppNumber?: string;
  userStatus?: UserStatus;
}

export class UserService {
  private userInfoDao: UserInfoDao;

  constructor() {
    console.log("Initializing UserService...");
    this.userInfoDao = new UserInfoDao();
  }

  async createUser(input: CreateUserInput) {
    console.log("Creating user with input:", input);
    try {
      const token = uuidv4();
      const result = await this.userInfoDao.createUserInfo(
        input.userId,
        input.userFullName,
        input.userWhatsAppNumber,
        UserStatus.NEW,
        "USER",
        token
      );

      console.log("User created successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in createUser:", { error, input });
      throw error;
    }
  }

  async getUserById(userId: string) {
    console.log("Getting user by ID:", userId);
    try {
      const result = await this.userInfoDao.getUserInfoByUserId(userId);
      console.log("User retrieved successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in getUserById:", { error, userId });
      throw error;
    }
  }

  async updateUser(userId: string, updates: UpdateUserInput) {
    console.log("Updating user:", { userId, updates });
    try {
      const result = await this.userInfoDao.updateUserInfo(userId, updates);
      console.log("User updated successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in updateUser:", { error, userId, updates });
      throw error;
    }
  }

  async deleteUser(userId: string) {
    console.log("Deleting user:", userId);
    try {
      const result = await this.userInfoDao.deleteUserInfo(userId);
      console.log("User deleted successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in deleteUser:", { error, userId });
      throw error;
    }
  }
}
