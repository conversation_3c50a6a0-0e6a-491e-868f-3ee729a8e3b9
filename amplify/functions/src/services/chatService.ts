import { MessagePlatform, MessageType } from "../config/enums";
import { MessageDao } from "../db/messageDao";
import { MessageService } from "../external/MessageService";
import { WhatsAppPayload } from "../types/whatsappPayload";
import { v4 as uuidv4 } from "uuid";
import { CaseService } from "../services/caseService";

interface MessageStatusUpdate {
  caseId: string;
  messageId: string;
  messageStatus: "DELIVERED" | "READ";
}

export class ChatService {
  private messageDao: MessageDao = new MessageDao();
  private caseService: CaseService;
  private messageService: MessageService = new MessageService();

  constructor(caseService: CaseService) {
    this.caseService = caseService;
  }

  /**
   * Processes outbound messages and sends them via the appropriate platform
   */
  private async processOutboundMessage(
    messageService: MessageService,
    payload: any
  ) {
    console.log(
      `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Processing outbound message:`,
      {
        recipientNumber: payload.recipientNumber,
        messageType: payload.messageType,
        messagePlatform: payload.messagePlatform,
      }
    );

    try {
      const messageType = payload.messageType.toLowerCase();

      let response;
      switch (messageType) {
        case MessageType.TEXT:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending text message:`,
            {
              recipientNumber: payload.recipientNumber,
              textLength: payload.content.text.length,
              textBeingSent: payload.content.text,
              hasPreviewUrl: !!payload.content.previewUrl,
            }
          );
          response = await messageService.sendTextMessage(
            payload.recipientNumber,
            payload.content.text,
            {
              messageId: payload.contextMessageId,
              previewUrl: payload.content.previewUrl,
            },
            payload.messagePlatform
          );
          break;

        case MessageType.IMAGE:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending image message:`,
            {
              recipientNumber: payload.recipientNumber,
              imageUrl: payload.content.url,
              hasCaption: !!payload.content.caption,
            }
          );
          response = await messageService.sendImageMessage(
            payload.recipientNumber,
            payload.content.url,
            payload.content.caption || "",
            {
              messageId: payload.contextMessageId,
            },
            payload.messagePlatform
          );
          break;

        case MessageType.INTERACTIVE_REPLY:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending interactive reply message:`,
            {
              recipientNumber: payload.recipientNumber,
              buttonCount: payload.content.action?.buttons?.length || 0,
            }
          );
          response = await messageService.sendInteractiveMessage(
            payload.recipientNumber,
            payload.content,
            {
              messageId: payload.contextMessageId,
            },
            payload.messagePlatform
          );
          break;

        case MessageType.INTERACTIVE_LIST:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending interactive list message:`,
            {
              recipientNumber: payload.recipientNumber,
              sectionCount: payload.content.sections?.length || 0,
            }
          );
          response = await messageService.sendInteractiveMessage(
            payload.recipientNumber,
            payload.content,
            {
              messageId: payload.contextMessageId,
            },
            payload.messagePlatform
          );
          break;

        default:
          throw new Error(`Unsupported message type: ${messageType}`);
      }

      console.log(
        `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Message sent successfully:`,
        {
          messageId: response?.messages?.[0]?.id,
          recipientId: response?.contacts?.[0]?.wa_id,
        }
      );

      return response;
    } catch (error) {
      console.error(
        `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Error processing outbound message:`,
        {
          error,
          errorType: error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage: error instanceof Error ? error.message : "Unknown error",
          recipientNumber: payload.recipientNumber,
          messageType: payload.messageType,
        }
      );
      throw error;
    }
  }

  async sendMessage(payload: any) {
    console.log("[ChatService][sendMessage] Starting message send:", {
      recipientNumber: payload.recipientNumber,
      messageType: payload.messageType,
      messagePlatform: payload.messagePlatform,
    });

    console.log(
      "[ChatService][sendMessage] Full payload:",
      JSON.stringify(payload, null, 2)
    );

    WhatsAppPayload.validate(payload);

    const response = await this.processOutboundMessage(
      this.messageService,
      payload
    );

    const messageId = response?.messages?.[0]?.id || `camid_${uuidv4()}`; // In case of message Source == chat app we need to create a messsage id

    console.log(
      `[ChatService][sendMessage] tid=${messageId} Message sent successfully:`,
      {
        recipientNumber: payload.recipientNumber,
        messageType: payload.messageType,
      }
    );

    // Save the outbound message
    try {
      console.log(
        `[ChatService][sendMessage] tid=${messageId} Saving message to database:`,
        {
          caseId: payload.caseId,
          messageType: payload.messageType,
          messagePlatform: payload.messagePlatform,
        }
      );

      // For interactive messages, store as just "INTERACTIVE"
      payload.messageType = payload.messageType
        .toUpperCase()
        .startsWith("INTERACTIVE")
        ? "INTERACTIVE"
        : payload.messageType;

      let messageContent = payload.content;
      if (payload.messageType.toUpperCase() === "TEXT") {
        messageContent = {
          body: {
            body: payload.content.text,
          },
        };
      } else if (payload.messageType.toUpperCase() === "IMAGE") {
        messageContent = {
          url: payload.content.url,
          caption: payload.content.caption || "",
        };
      }

      // In case of message Source == chat app we need to create a messsage id
      const savedMessage = await this.messageDao.createMessage(
        payload.caseId,
        messageId,
        payload.recipientNumber,
        "OUTBOUND",
        "SENT",
        payload.messageType.toUpperCase(),
        messageContent,
        payload.messagePlatform,
        payload.messageOrigin
      );

      console.log(
        `[ChatService][sendMessage] tid=${response.messages?.[0]?.id} Message saved successfully:`,
        {
          caseId: payload.caseId,
        },
        savedMessage
      );

      await this.caseService.addMessageToCase(payload.recipientNumber, payload.caseId, messageId);
    } catch (error) {
      console.error(
        `[ChatService][sendMessage] tid=${messageId} Error saving message:`,
        {
          error,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
          caseId: payload.caseId,
        }
      );
      // Not throwing error to ensure API response is still sent
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Message sent successfully",
        messageId: messageId,
        recipientNumber: payload.recipientNumber,
        messageType: payload.messageType,
      }),
    };
  }

  /**
   * Update message statuses
   */
  async updateMessageStatuses(
    webhookBody: any,
    statusUpdates: Array<{
      caseId: string;
      messageId: string;
      messageStatus: "DELIVERED" | "READ";
    }>
  ) {
    console.log(
      `[ChatService][updateMessageStatuses] Processing status updates:`,
      {
        count: statusUpdates.length,
        statuses: statusUpdates.map((u) => u.messageStatus),
      }
    );

    try {
      await this.messageDao.updateMessageStatuses(statusUpdates);
      console.log(
        `[ChatService][updateMessageStatuses] Status updates processed successfully`
      );
    } catch (error) {
      console.error(
        `[ChatService][updateMessageStatuses] Error updating message statuses:`,
        error
      );
      throw error;
    }
  }
}
