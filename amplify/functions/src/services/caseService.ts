import { CaseStatus } from "../config/enums";
import { CaseDao } from "../db/caseDao";
import { v4 as uuidv4 } from "uuid";

export interface CreateCaseInput {
  userId: string;
  caseStatus?: CaseStatus;
  messages?: string[];
}

export interface UpdateCaseInput {
  caseId: string;
  userId: string;
  caseStatus?: CaseStatus;
  useCase?: string;
  contextReady?: boolean;
  userOutfitImage?: string;
  isProfileShown?: boolean;
}

export interface ListCasesInput {
  userId: string;
  status?: CaseStatus;
  limit?: number;
  startKey?: any;
}

export class CaseService {
  private caseDao: CaseDao;

  constructor(aiService?: any) {
    console.log("Initializing CaseService...");
    this.caseDao = new CaseDao();
  }

  async createCase(input: CreateCaseInput) {
    console.log("Creating case with input:", input);
    try {
      const caseId = `${Number.MAX_SAFE_INTEGER - Date.now()}-${uuidv4()}`;
      const caseStartTime = new Date().toISOString();
      console.log("Generated case details:", { caseId, caseStartTime });

      const result = await this.caseDao.createCase(
        input.userId,
        caseId,
        caseStartTime,
        input.caseStatus || CaseStatus.NEW,
        input.messages || []
      );

      console.log("Case created successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in createCase:", { error, input });
      throw error;
    }
  }

  async getCaseById(userId: string, caseId: string) {
    console.log("Getting case by ID:", { userId, caseId });
    try {
      const result = await this.caseDao.getCaseById(userId, caseId);
      console.log("Case retrieved successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in getCaseById:", { error, userId, caseId });
      throw error;
    }
  }

  async updateCase(input: UpdateCaseInput) {
    console.log("Updating case with input:", input);
    try {
      const updateData: any = {};

      if (input.caseStatus) updateData.caseStatus = input.caseStatus;
      if (input.useCase) updateData.useCase = input.useCase;
      if (input.contextReady !== undefined) updateData.contextReady = input.contextReady;
      if (input.userOutfitImage) updateData.userOutfitImage = input.userOutfitImage;
      if (input.isProfileShown !== undefined) updateData.isProfileShown = input.isProfileShown;

      const result = await this.caseDao.updateCase(
        input.userId,
        input.caseId,
        updateData
      );

      console.log("Case updated successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in updateCase:", { error, input });
      throw error;
    }
  }

  async listCases(params: ListCasesInput) {
    console.log("Listing cases with params:", params);
    try {
      const result = await this.caseDao.listCasesByUser(
        params.userId,
        params.limit || 50,
        params.startKey
      );

      // Filter by status if provided
      let filteredItems = result.items || [];
      if (params.status) {
        filteredItems = filteredItems.filter(
          (caseItem: any) => caseItem.caseStatus === params.status
        );
      }

      const finalResult = {
        items: filteredItems,
        lastEvaluatedKey: result.lastEvaluatedKey,
      };

      console.log("Cases listed successfully:", finalResult);
      return finalResult;
    } catch (error) {
      console.error("Error in listCases:", { error, params });
      throw error;
    }
  }

  /**
   * Add message to case - helper method for ChatService
   */
  async addMessageToCase(userId: string, caseId: string, messageId: string) {
    console.log("Adding message to case:", { userId, caseId, messageId });
    try {
      // Get current case
      const currentCase = await this.caseDao.getCaseById(userId, caseId);
      if (!currentCase) {
        throw new Error(`Case not found: ${caseId}`);
      }

      // Add message to messages array
      const messages = currentCase.messages || [];
      messages.push(messageId);

      // Update case with new message
      await this.caseDao.updateCase(userId, caseId, { messages });
      console.log("Message added to case successfully");
    } catch (error) {
      console.error("Error adding message to case:", { error, userId, caseId, messageId });
      throw error;
    }
  }

  async closeCase(userId: string, caseId: string) {
    console.log("Closing case:", { userId, caseId });
    try {
      const result = await this.caseDao.updateCase(userId, caseId, {
        caseStatus: CaseStatus.CLOSED,
      });
      console.log("Case closed successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in closeCase:", { error, userId, caseId });
      throw error;
    }
  }
}
