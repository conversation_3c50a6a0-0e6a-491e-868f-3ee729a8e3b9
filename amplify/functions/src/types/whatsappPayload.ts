import { MessagePlatform } from "../config/enums";

export interface WhatsAppPayloadData {
  to: string;
  type: "text" | "image" | "interactive";
  text?: {
    body: string;
  };
  image?: {
    link: string;
    caption?: string;
  };
  interactive?: {
    type: "button" | "list";
    body?: {
      text: string;
    };
    action?: {
      buttons?: Array<{
        type: "reply";
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: Array<{
        title: string;
        rows: Array<{
          id: string;
          title: string;
          description?: string;
        }>;
      }>;
    };
  };
  platform?: MessagePlatform;
}

export class WhatsAppPayload {
  public to: string;
  public type: "text" | "image" | "interactive";
  public text?: { body: string };
  public image?: { link: string; caption?: string };
  public interactive?: any;
  public platform: MessagePlatform;

  constructor(data: WhatsAppPayloadData) {
    this.to = data.to;
    this.type = data.type;
    this.text = data.text;
    this.image = data.image;
    this.interactive = data.interactive;
    this.platform = data.platform || MessagePlatform.WHATSAPP;
  }

  static validate(data: any): WhatsAppPayload {
    // Basic validation
    if (!data.to) {
      throw new Error("WhatsApp payload must have 'to' field");
    }

    if (!data.type) {
      throw new Error("WhatsApp payload must have 'type' field");
    }

    if (!["text", "image", "interactive"].includes(data.type)) {
      throw new Error("WhatsApp payload type must be 'text', 'image', or 'interactive'");
    }

    // Type-specific validation
    if (data.type === "text" && (!data.text || !data.text.body)) {
      throw new Error("Text message must have text.body");
    }

    if (data.type === "image" && (!data.image || !data.image.link)) {
      throw new Error("Image message must have image.link");
    }

    if (data.type === "interactive" && !data.interactive) {
      throw new Error("Interactive message must have interactive content");
    }

    return new WhatsAppPayload(data);
  }

  static createTextMessage(to: string, message: string, platform?: MessagePlatform): WhatsAppPayload {
    return new WhatsAppPayload({
      to,
      type: "text",
      text: { body: message },
      platform: platform || MessagePlatform.WHATSAPP,
    });
  }

  static createImageMessage(
    to: string,
    imageUrl: string,
    caption?: string,
    platform?: MessagePlatform
  ): WhatsAppPayload {
    return new WhatsAppPayload({
      to,
      type: "image",
      image: { link: imageUrl, caption },
      platform: platform || MessagePlatform.WHATSAPP,
    });
  }

  static createButtonMessage(
    to: string,
    bodyText: string,
    buttons: Array<{ id: string; title: string }>,
    platform?: MessagePlatform
  ): WhatsAppPayload {
    return new WhatsAppPayload({
      to,
      type: "interactive",
      interactive: {
        type: "button",
        body: { text: bodyText },
        action: {
          buttons: buttons.map((btn) => ({
            type: "reply",
            reply: {
              id: btn.id,
              title: btn.title,
            },
          })),
        },
      },
      platform: platform || MessagePlatform.WHATSAPP,
    });
  }

  static createListMessage(
    to: string,
    bodyText: string,
    sections: Array<{
      title: string;
      rows: Array<{ id: string; title: string; description?: string }>;
    }>,
    platform?: MessagePlatform
  ): WhatsAppPayload {
    return new WhatsAppPayload({
      to,
      type: "interactive",
      interactive: {
        type: "list",
        body: { text: bodyText },
        action: { sections },
      },
      platform: platform || MessagePlatform.WHATSAPP,
    });
  }

  toJSON(): WhatsAppPayloadData {
    return {
      to: this.to,
      type: this.type,
      text: this.text,
      image: this.image,
      interactive: this.interactive,
      platform: this.platform,
    };
  }
}
