import { MessagePlatform } from "../config/enums";

export interface WhatsAppMessage {
  id?: string;
  type: "text" | "image" | "interactive";
  text?: {
    body: string;
  };
  image?: {
    link: string;
    caption?: string;
  };
  interactive?: {
    type: "button" | "list";
    body?: {
      text: string;
    };
    action?: {
      buttons?: Array<{
        type: "reply";
        reply: {
          id: string;
          title: string;
        };
      }>;
      sections?: Array<{
        title: string;
        rows: Array<{
          id: string;
          title: string;
          description?: string;
        }>;
      }>;
    };
  };
}

export interface MessageResponse {
  messages?: Array<{
    id: string;
  }>;
  contacts?: Array<{
    wa_id: string;
  }>;
  success: boolean;
  error?: string;
}

export class MessageService {
  private whatsappApiUrl: string;
  private accessToken: string;

  constructor() {
    this.whatsappApiUrl = process.env.WHATSAPP_API_URL || "https://graph.facebook.com/v21.0";
    this.accessToken = process.env.WHATSAPP_ACCESS_TOKEN || "EAA1y2Bhk2S0BPXx0nmkkoZABMRd3yQYZBo2NQgF5EeuxrYTywdf4I0ZBSM3238UYhXkqVZANWYdqF39xsRae8cojzUf8M7JmLrRdo3yVoIiZADbpTrkFihAdHtxOHZBvNDPwSpQl22uoOAEsuBsHowtgBUzdwxh3TUXSnHKE9WcesJXtfZAdIwnjjsa5mhszgZDZD";
  }

  async sendTextMessage(
    to: string,
    message: string,
    options?: { messageId?: string; previewUrl?: boolean },
    platform: MessagePlatform = MessagePlatform.WHATSAPP
  ): Promise<MessageResponse> {
    console.log("[MessageService][sendTextMessage] Sending text message:", { to, message, platform });

    try {
      if (platform === MessagePlatform.NATIVE) {
        // For native platform, just return success (mock implementation)
        return {
          messages: [{ id: `native_${Date.now()}` }],
          success: true,
        };
      }

      // WhatsApp Cloud API implementation
      if (platform === MessagePlatform.WHATSAPP) {
        const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID || "440678912463914";

        if (!phoneNumberId || !this.accessToken) {
          console.error("[MessageService][sendTextMessage] Missing WhatsApp configuration");
          return {
            success: false,
            error: "WhatsApp configuration missing",
          };
        }

        const url = `${this.whatsappApiUrl}/${phoneNumberId}/messages`;
        const payload: any = {
          messaging_product: "whatsapp",
          recipient_type: "individual",
          to: to,
          type: "text",
          text: {
            preview_url: options?.previewUrl || false,
            body: message
          }
        };

        // Add context if replying to a message
        if (options?.messageId) {
          payload.context = {
            message_id: options.messageId
          };
        }

        console.log("[MessageService][sendTextMessage] Sending to WhatsApp API:", {
          url,
          payload,
          accessTokenLength: this.accessToken.length,
          phoneNumberId
        });

        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const responseData = await response.json();
        console.log("[MessageService][sendTextMessage] WhatsApp API response:", responseData);

        if (response.ok && responseData.messages && responseData.messages[0]) {
          return {
            messages: responseData.messages,
            contacts: responseData.contacts,
            success: true,
          };
        } else {
          console.error("[MessageService][sendTextMessage] WhatsApp API error:", responseData);
          return {
            success: false,
            error: responseData.error?.message || "WhatsApp API error",
          };
        }
      }

      // Default fallback
      return {
        messages: [{ id: `fallback_${Date.now()}` }],
        success: true,
      };
    } catch (error) {
      console.error("[MessageService][sendTextMessage] Error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  async sendImageMessage(
    to: string,
    imageUrl: string,
    caption?: string,
    options?: { messageId?: string },
    platform: MessagePlatform = MessagePlatform.WHATSAPP
  ): Promise<MessageResponse> {
    console.log("[MessageService][sendImageMessage] Sending image message:", { to, imageUrl, caption, platform });

    try {
      if (platform === MessagePlatform.NATIVE) {
        // For native platform, just return success (mock implementation)
        return {
          messages: [{ id: `native_img_${Date.now()}` }],
          success: true,
        };
      }

      // WhatsApp Cloud API implementation
      if (platform === MessagePlatform.WHATSAPP) {
        const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;

        if (!phoneNumberId || !this.accessToken) {
          console.error("[MessageService][sendImageMessage] Missing WhatsApp configuration");
          return {
            success: false,
            error: "WhatsApp configuration missing",
          };
        }

        const url = `${this.whatsappApiUrl}/${phoneNumberId}/messages`;
        const payload: any = {
          messaging_product: "whatsapp",
          recipient_type: "individual",
          to: to,
          type: "image",
          image: {
            link: imageUrl
          }
        };

        // Add caption if provided
        if (caption) {
          payload.image.caption = caption;
        }

        // Add context if replying to a message
        if (options?.messageId) {
          payload.context = {
            message_id: options.messageId
          };
        }

        console.log("[MessageService][sendImageMessage] Sending to WhatsApp API:", { url, to });

        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const responseData = await response.json();
        console.log("[MessageService][sendImageMessage] WhatsApp API response:", responseData);

        if (response.ok && responseData.messages && responseData.messages[0]) {
          return {
            messages: responseData.messages,
            contacts: responseData.contacts,
            success: true,
          };
        } else {
          console.error("[MessageService][sendImageMessage] WhatsApp API error:", responseData);
          return {
            success: false,
            error: responseData.error?.message || "WhatsApp API error",
          };
        }
      }

      // Default fallback
      return {
        messages: [{ id: `fallback_img_${Date.now()}` }],
        success: true,
      };
    } catch (error) {
      console.error("[MessageService][sendImageMessage] Error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  async sendInteractiveMessage(
    to: string,
    message: WhatsAppMessage,
    options?: { messageId?: string },
    platform: MessagePlatform = MessagePlatform.WHATSAPP
  ): Promise<MessageResponse> {
    console.log("[MessageService][sendInteractiveMessage] Sending interactive message:", { to, message, platform });

    try {
      if (platform === MessagePlatform.NATIVE) {
        // For native platform, just return success (mock implementation)
        return {
          messages: [{ id: `native_int_${Date.now()}` }],
          success: true,
        };
      }

      // WhatsApp Cloud API implementation
      if (platform === MessagePlatform.WHATSAPP) {
        const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;

        if (!phoneNumberId || !this.accessToken) {
          console.error("[MessageService][sendInteractiveMessage] Missing WhatsApp configuration");
          return {
            success: false,
            error: "WhatsApp configuration missing",
          };
        }

        const url = `${this.whatsappApiUrl}/${phoneNumberId}/messages`;
        const payload: any = {
          messaging_product: "whatsapp",
          recipient_type: "individual",
          to: to,
          type: "interactive",
          interactive: message.interactive
        };

        // Add context if replying to a message
        if (options?.messageId) {
          payload.context = {
            message_id: options.messageId
          };
        }

        console.log("[MessageService][sendInteractiveMessage] Sending to WhatsApp API:", { url, to });

        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.accessToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        const responseData = await response.json();
        console.log("[MessageService][sendInteractiveMessage] WhatsApp API response:", responseData);

        if (response.ok && responseData.messages && responseData.messages[0]) {
          return {
            messages: responseData.messages,
            contacts: responseData.contacts,
            success: true,
          };
        } else {
          console.error("[MessageService][sendInteractiveMessage] WhatsApp API error:", responseData);
          return {
            success: false,
            error: responseData.error?.message || "WhatsApp API error",
          };
        }
      }

      // Default fallback
      return {
        messages: [{ id: `fallback_int_${Date.now()}` }],
        success: true,
      };
    } catch (error) {
      console.error("[MessageService][sendInteractiveMessage] Error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}
