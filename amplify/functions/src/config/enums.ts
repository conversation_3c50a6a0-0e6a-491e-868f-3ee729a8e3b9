export enum MessageDirection {
  INBOUND = "INBOUND",
  OUTBOUND = "OUTBOUND",
}

export enum MessageStatus {
  SENT = "SENT",
  DELIVERED = "DELIVERED",
  READ = "READ",
  RECEIVED = "RECEIVED",
}

export enum UserStatus {
  NEW = "NEW",
  ONBOARDED = "ONBOARDED",
  INACTIVE = "INACTIVE",
}

export enum UserRole {
  ADMIN = "ADMIN",
  USER = "USER",
  GUEST = "GUEST",
}

export const MessageType = {
  TEXT: "text",
  IMAGE: "image",
  AUDIO: "audio",
  DOCUMENT: "document",
  VIDEO: "video",
  STICKER: "sticker",
  LOCATION: "location",
  CONTACTS: "contacts",
  INTERACTIVE: "interactive",
  INTERACTIVE_REPLY: "interactive_reply",
  INTERACTIVE_LIST: "interactive_list",
  INTERACTIVE_INPUT: "interactive_input",
};

export enum CaseStatus {
  NEW = "NEW",
  USECASE_IDENTIFIED = "USECASE_IDENTIFIED",
  CONTEXT_GATHERED = "CONTEXT_GATHERED",
  PROFILE_READY = "PROFILE_READY",
  OUTPUT_SENT = "OUTPUT_SENT",
  FEEDBACK_LOOP = "FEEDBACK_LOOP",
  CLOSED = "CLOSED",
}

export enum UseCase {
  REVIEW_OUTFIT = "REVIEW_OUTFIT",
  OUTFIT_CURATION = "OUTFIT_CURATION",
  AMA = "AMA",
}

export enum MessagePlatform {
  WHATSAPP = "WHATSAPP",
  NATIVE = "NATIVE",
  WEB = "WEB",
}

export enum MessageOrigin {
  MODEL = "MODEL",
  USER = "USER",
  SYSTEM = "SYSTEM",
}

export enum UseCaseResponseType {
  INTENT_CLASSIFIED = "INTENT_CLASSIFIED",
  INTENT_REQUIRED = "INTENT_REQUIRED",
}

export enum MaleBodyType {
  RECTANGLE = "RECTANGLE",
  TRIANGLE = "TRIANGLE",
  INVERTED_TRIANGLE = "INVERTED_TRIANGLE",
  OVAL = "OVAL",
  TRAPEZOID = "TRAPEZOID",
}

export enum FemaleBodyType {
  RECTANGLE = "RECTANGLE",
  PEAR = "PEAR",
  INVERTED_TRIANGLE = "INVERTED_TRIANGLE",
  APPLE = "APPLE",
  HOURGLASS = "HOURGLASS",
}
