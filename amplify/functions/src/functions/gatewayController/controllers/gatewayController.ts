import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { MAISService } from "../../../services/maisService";
import { MessageOrigin } from "../../../config/enums";
import { GatewayService } from "../../../services/gatewayService";

/**
 * Gateway Controller - Main entry point for all message processing requests
 *
 * This controller serves as the single entry point for the application, handling
 * all incoming messages and orchestrating the complete message processing pipeline
 * including user management, case management, and AI-powered styling services.
 */
export class GatewayController {
  private gatewayService: GatewayService;

  constructor() {
    this.gatewayService = new GatewayService();
  }

  /**
   * Handles incoming message processing requests
   *
   * This method orchestrates the complete message processing pipeline:
   * 1. Validates and parses the incoming request
   * 2. Creates or retrieves user information
   * 3. Manages case lifecycle (creates new cases or uses existing ones)
   * 4. Stores the message in the database
   * 5. Processes the message through the MAIS (AI styling) service
   *
   * @param event - API Gateway proxy event containing the message data
   * @returns Promise<APIGatewayProxyResult> - HTTP response with processing results
   */
  async handleMessage(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    let messageId = "unknown";
    try {
      // Validate request body exists
      if (!event.body) {
        console.error("[GatewayController][handleMessage] Request body is missing");
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: "Request body is required",
          }),
        };
      }

      // Parse request body
      let requestData;
      try {
        requestData = JSON.parse(event.body);
        messageId = requestData.messageId || "unknown";
        console.log(
          `[GatewayController][handleMessage] tid=${messageId} Request received:`,
          {
            messageId,
            userId: requestData.userId,
            messageType: requestData.messageType,
            messagePlatform: requestData.messagePlatform,
          }
        );
      } catch (parseError) {
        console.error("[GatewayController][handleMessage] Invalid JSON in request body:", parseError);
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: "Invalid JSON in request body",
          }),
        };
      }

      // Validate required fields
      const { userId, userFullName, userWhatsAppNumber, messageType, messageContent, messagePlatform } = requestData;
      
      if (!userId || !messageType || !messageContent || !messagePlatform) {
        console.error(
          `[GatewayController][handleMessage] tid=${messageId} Missing required fields:`,
          {
            userId: !!userId,
            messageType: !!messageType,
            messageContent: !!messageContent,
            messagePlatform: !!messagePlatform,
          }
        );
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: "Missing required fields: userId, messageType, messageContent, messagePlatform",
          }),
        };
      }

      console.log(
        `[GatewayController][handleMessage] tid=${messageId} Processing message for user:`,
        {
          userId,
          messageType,
          messagePlatform,
        }
      );

      // Step 1: Get or create user
      const userData = await this.gatewayService.getOrCreateUser(
        userId,
        userFullName || "Unknown User",
        userWhatsAppNumber || "Unknown Number",
        messageId
      );

      console.log(
        `[GatewayController][handleMessage] tid=${messageId} User data retrieved:`,
        {
          userId: userData?.userId || userId,
          userStatus: userData?.userStatus || "NEW",
        }
      );

      // Step 2: Get or create active case
      let caseData: any = await this.gatewayService.getActiveCase(userId, messageId);

      if (!caseData) {
        console.log(
          `[GatewayController][handleMessage] tid=${messageId} No active case found, creating new case`
        );
        caseData = await this.gatewayService.createNewCase(userId, messageId);
      }

      const caseId = caseData.caseId;
      console.log(
        `[GatewayController][handleMessage] tid=${messageId} Case data retrieved:`,
        {
          caseId,
          caseStatus: caseData.caseStatus,
        }
      );

      // Step 3: Store the incoming message
      await this.gatewayService.storeMessage({
        caseId,
        userId,
        messageId,
        messageType,
        messageDirection: "INBOUND",
        messageContent,
        messagePlatform,
        messageOrigin: MessageOrigin.USER,
        messageContext: requestData.messageContext,
      });

      console.log(
        `[GatewayController][handleMessage] tid=${messageId} Message stored successfully`
      );

      // Step 4: Process message through MAIS service
      try {
        const maisService = new MAISService();

        // TODO: Timeout Configuration
        // Consider adding a timeout wrapper for MAIS processing to prevent
        // long-running requests from timing out the Lambda function
        // Current Lambda timeout is 5 minutes, but user experience may require faster responses
        const maisResult = await maisService.processMessage(
          caseId,
          userId,
          messageId,
          messagePlatform
        );

        console.log(
          `[GatewayController][handleMessage] tid=${messageId} MAISService.processMessage result:`,
          maisResult
        );

        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Message processed successfully",
            caseId,
            maissResult: maisResult,
          }),
        };
      } catch (error) {
        console.error(
          `[GatewayController][handleMessage] tid=${messageId} Error calling MAISService.processMessage:`,
          {
            error,
            caseId,
            userId,
            messageId,
            errorMessage:
              error instanceof Error ? error.message : "Unknown error",
          }
        );

        return {
          statusCode: 500,
          body: JSON.stringify({
            message: "Message MAIS processing failed",
            caseId,
            error: error instanceof Error ? error.message : "Unknown error",
          }),
        };
      }
    } catch (error) {
      console.error(`[GatewayController][handleMessage] tid=${messageId} Unhandled Error:`, {
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });

      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Internal server error",
          error: error instanceof Error ? error.message : "Unknown error",
        }),
      };
    }
  }
}
