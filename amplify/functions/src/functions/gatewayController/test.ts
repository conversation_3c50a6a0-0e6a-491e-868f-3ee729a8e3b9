/**
 * Simple test to verify the gateway controller can be imported and instantiated
 */
import { GatewayController } from './controllers/gatewayController';

async function testGatewayController() {
  console.log('Testing Gateway Controller...');
  
  try {
    // Test instantiation
    const controller = new GatewayController();
    console.log('✅ Gateway Controller instantiated successfully');
    
    // Test mock message handling
    const mockEvent = {
      body: JSON.stringify({
        userId: 'test-user-123',
        userFullName: 'Test User',
        userWhatsAppNumber: '+1234567890',
        messageId: 'test-message-123',
        messageType: 'text',
        messageDirection: 'INBOUND',
        messageContent: {
          text: 'Hello, I need help with my outfit'
        },
        messagePlatform: 'WHATSAPP',
        messageOrigin: 'USER'
      }),
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    console.log('🧪 Testing message handling...');
    const result = await controller.handleMessage(mockEvent as any);
    console.log('✅ Message handled successfully:', result);
    
    console.log('🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testGatewayController()
    .then(() => {
      console.log('Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

export { testGatewayController };
