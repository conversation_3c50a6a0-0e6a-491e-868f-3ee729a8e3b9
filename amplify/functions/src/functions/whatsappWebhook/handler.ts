import { APIGatewayProxyHand<PERSON>, APIGatewayProxyResult } from "aws-lambda";
import { router } from "./routes";

console.log(`[WHATSAPP_WEBHOOK] Initialized Controller`);

export const handler: APIGatewayProxyHandler = async (
  event
): Promise<APIGatewayProxyResult> => {
  console.log("WhatsApp webhook request received:", {
    path: event.path,
    method: event.httpMethod,
    queryStringParameters: event.queryStringParameters,
    body: event.body,
  });

  try {
    return await router(event);
  } catch (error) {
    console.error("Error in WhatsApp webhook handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};
