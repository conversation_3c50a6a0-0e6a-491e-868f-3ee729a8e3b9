import { MessageDao } from "../../../db/messageDao";

/**
 * WhatsApp Status Update Handler
 * 
 * Handles WhatsApp message status updates (delivered, read, etc.)
 */
export class WhatsAppStatusUpdateHandler {
  private messageDao: MessageDao;

  constructor() {
    this.messageDao = new MessageDao();
  }

  /**
   * Handles status updates from WhatsApp webhook
   */
  async handleStatusUpdate(webhookBody: any): Promise<boolean> {
    console.log("[WhatsAppStatusUpdateHandler][handleStatusUpdate] Processing status update");

    try {
      const statuses = webhookBody.entry[0].changes[0].value.statuses;
      
      if (!statuses || statuses.length === 0) {
        console.log("[WhatsAppStatusUpdateHandler][handleStatusUpdate] No statuses to process");
        return false;
      }

      const statusUpdates = [];

      for (const status of statuses) {
        const messageId = status.id;
        const statusType = status.status;
        const recipientId = status.recipient_id;

        console.log("[WhatsAppStatusUpdateHandler][handleStatusUpdate] Processing status:", {
          messageId,
          statusType,
          recipientId
        });

        // Map WhatsApp status to our internal status
        const mappedStatus = this.mapWhatsAppStatus(statusType);
        
        if (mappedStatus) {
          // Find the case for this message
          // Note: We need to find the case by looking up the message
          // This is a simplified approach - in production you might want to optimize this
          const caseId = await this.findCaseIdByMessageId(messageId);
          
          if (caseId) {
            statusUpdates.push({
              caseId,
              messageId,
              messageStatus: mappedStatus
            });
          } else {
            console.warn("[WhatsAppStatusUpdateHandler][handleStatusUpdate] Could not find case for message:", messageId);
          }
        }
      }

      if (statusUpdates.length > 0) {
        await this.messageDao.updateMessageStatuses(statusUpdates);
        console.log("[WhatsAppStatusUpdateHandler][handleStatusUpdate] Updated statuses for messages:", 
          statusUpdates.map(u => u.messageId));
        return true;
      }

      return false;

    } catch (error) {
      console.error("[WhatsAppStatusUpdateHandler][handleStatusUpdate] Error processing status update:", error);
      return false;
    }
  }

  /**
   * Maps WhatsApp status to our internal message status
   */
  private mapWhatsAppStatus(whatsappStatus: string): "DELIVERED" | "READ" | null {
    switch (whatsappStatus) {
      case "delivered":
        return "DELIVERED";
      case "read":
        return "READ";
      default:
        console.log("[WhatsAppStatusUpdateHandler][mapWhatsAppStatus] Unknown status:", whatsappStatus);
        return null;
    }
  }

  /**
   * Finds the case ID for a given message ID
   * This is a helper method to locate which case a message belongs to
   */
  private async findCaseIdByMessageId(messageId: string): Promise<string | null> {
    try {
      // This is a simplified implementation
      // In a real scenario, you might want to add an index or optimize this lookup
      console.log("[WhatsAppStatusUpdateHandler][findCaseIdByMessageId] Looking up case for message:", messageId);
      
      // For now, we'll return null and log a warning
      // You can implement a more sophisticated lookup mechanism based on your needs
      console.warn("[WhatsAppStatusUpdateHandler][findCaseIdByMessageId] Case lookup not implemented for message:", messageId);
      return null;

    } catch (error) {
      console.error("[WhatsAppStatusUpdateHandler][findCaseIdByMessageId] Error finding case:", error);
      return null;
    }
  }
}
