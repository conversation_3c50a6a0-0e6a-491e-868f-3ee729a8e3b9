/**
 * WhatsApp Message Parser Service
 * 
 * Extracts and parses message data from WhatsApp Cloud API webhook payloads
 */

export interface ParsedMessageData {
  userId: string;
  whatsAppNumber: string;
  fullName: string;
  messageId: string;
  messageType: "TEXT" | "IMAGE" | "INTERACTIVE" | "LOG";
  messageContent: any;
}

export class WhatsAppMessageParser {
  
  /**
   * Parses incoming message data from WhatsApp webhook payload
   */
  parseIncomingMessage(webhookBody: any): ParsedMessageData {
    console.log("[WhatsAppMessageParser][parseIncomingMessage] Parsing webhook body");

    try {
      const change = webhookBody.entry[0].changes[0].value;
      const message = change.messages[0];
      const contact = change.contacts[0];

      // Extract basic information
      // Use WhatsApp phone number as userId for consistent user profiles across platforms
      const userId = message.from; // This is the user's WhatsApp number
      const whatsAppNumber = message.from;
      const fullName = contact.profile.name;
      const messageId = message.id;
      const messageType = this.mapMessageType(message.type);

      // Extract message content based on type
      const messageContent = this.extractMessageContent(message);

      console.log("[WhatsAppMessageParser][parseIncomingMessage] Parsed message:", {
        userId,
        whatsAppNumber,
        fullName,
        messageId,
        messageType,
        contentType: typeof messageContent
      });

      return {
        userId,
        whatsAppNumber,
        fullName,
        messageId,
        messageType,
        messageContent
      };

    } catch (error) {
      console.error("[WhatsAppMessageParser][parseIncomingMessage] Error parsing message:", error);
      throw new Error("Failed to parse WhatsApp message");
    }
  }

  /**
   * Maps WhatsApp message types to our internal message types
   */
  private mapMessageType(whatsappType: string): "TEXT" | "IMAGE" | "INTERACTIVE" | "LOG" {
    switch (whatsappType) {
      case "text":
        return "TEXT";
      case "image":
        return "IMAGE";
      case "interactive":
        return "INTERACTIVE";
      default:
        console.log("[WhatsAppMessageParser][mapMessageType] Unknown message type:", whatsappType);
        return "LOG"; // Default to LOG for unknown types
    }
  }

  /**
   * Extracts message content based on message type
   */
  private extractMessageContent(message: any): any {
    const messageType = message.type;

    switch (messageType) {
      case "text":
        return {
          body: {
            body: message.text.body
          }
        };

      case "image":
        return {
          image: {
            id: message.image.id,
            mime_type: message.image.mime_type,
            sha256: message.image.sha256,
            caption: message.image.caption || ""
          }
        };

      case "interactive":
        if (message.interactive.button_reply) {
          return {
            interactive: {
              button_reply: {
                id: message.interactive.button_reply.id,
                title: message.interactive.button_reply.title
              }
            }
          };
        } else if (message.interactive.list_reply) {
          return {
            interactive: {
              list_reply: {
                id: message.interactive.list_reply.id,
                title: message.interactive.list_reply.title,
                description: message.interactive.list_reply.description
              }
            }
          };
        }
        return message.interactive;

      default:
        console.log("[WhatsAppMessageParser][extractMessageContent] Unknown message type:", messageType);
        return message;
    }
  }
}
