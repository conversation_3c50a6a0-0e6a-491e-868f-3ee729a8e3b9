import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { GatewayController } from "../../gatewayController/controllers/gatewayController";
import { MessagePlatform, MessageOrigin } from "../../../config/enums";
import { WhatsAppStatusUpdateHandler } from "../services/statusUpdateHandler";
import { WhatsAppMessageParser } from "../services/messageParser";

/**
 * WhatsApp Webhook Controller
 * 
 * Handles WhatsApp Cloud API webhook events including:
 * - Webhook verification for registration
 * - Incoming message processing
 * - Status updates (delivery, read receipts)
 */
export class WhatsAppWebhookController {
  private gatewayController: GatewayController;
  private statusUpdateHandler: WhatsAppStatusUpdateHandler;
  private messageParser: WhatsAppMessageParser;

  constructor() {
    this.gatewayController = new GatewayController();
    this.statusUpdateHandler = new WhatsAppStatusUpdateHandler();
    this.messageParser = new WhatsAppMessageParser();
  }

  /**
   * Handles WhatsApp webhook verification
   * This is called when <PERSON><PERSON><PERSON><PERSON> tries to verify the webhook URL
   */
  async verifyWebhook(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    console.log("[WhatsAppWebhookController][verifyWebhook] Webhook verification request received");

    try {
      const queryParams = event.queryStringParameters;
      const verifyToken = process.env.WHATSAPP_VERIFY_TOKEN;

      if (!queryParams) {
        console.error("[WhatsAppWebhookController][verifyWebhook] No query parameters provided");
        return {
          statusCode: 400,
          body: JSON.stringify({ message: "Missing query parameters" }),
        };
      }

      // Parse params from the webhook verification request
      const mode = queryParams["hub.mode"];
      const token = queryParams["hub.verify_token"];
      const challenge = queryParams["hub.challenge"];

      console.log("[WhatsAppWebhookController][verifyWebhook] Verification params:", {
        mode,
        token: token ? "***" : "missing",
        challenge: challenge ? "present" : "missing",
        expectedToken: verifyToken ? "***" : "missing"
      });

      // Check if a token and mode were sent
      if (mode && token) {
        // Check the mode and token sent are correct
        if (mode === "subscribe" && token === verifyToken) {
          console.log("[WhatsAppWebhookController][verifyWebhook] WEBHOOK_VERIFIED");
          return {
            statusCode: 200,
            body: challenge || "",
          };
        } else {
          console.error("[WhatsAppWebhookController][verifyWebhook] Token mismatch or invalid mode");
          return {
            statusCode: 403,
            body: JSON.stringify({ message: "Forbidden" }),
          };
        }
      }

      console.error("[WhatsAppWebhookController][verifyWebhook] Missing mode or token");
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "Missing mode or token" }),
      };
    } catch (error) {
      console.error("[WhatsAppWebhookController][verifyWebhook] Error:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({ message: "Internal server error" }),
      };
    }
  }

  /**
   * Handles incoming WhatsApp webhook events
   * This processes actual messages and status updates
   */
  async handleWebhook(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    console.log("[WhatsAppWebhookController][handleWebhook] Webhook event received");

    try {
      if (!event.body) {
        console.error("[WhatsAppWebhookController][handleWebhook] No request body provided");
        return {
          statusCode: 400,
          body: JSON.stringify({ message: "Request body is required" }),
        };
      }

      // Parse the webhook body
      const body = JSON.parse(event.body);
      console.log("[WhatsAppWebhookController][handleWebhook] Parsed webhook body:", JSON.stringify(body, null, 2));

      // Check if this is a status update
      if (body.entry && body.entry[0].changes && body.entry[0].changes[0].value.statuses) {
        console.log("[WhatsAppWebhookController][handleWebhook] Processing status update");
        const statusUpdated = await this.statusUpdateHandler.handleStatusUpdate(body);
        if (statusUpdated) {
          return {
            statusCode: 200,
            body: JSON.stringify({ message: "Status update processed successfully" }),
          };
        }
      }

      // Check if this is a message event
      if (body.object === "whatsapp_business_account" && 
          body.entry && 
          body.entry[0].changes && 
          body.entry[0].changes[0].value.messages) {
        
        console.log("[WhatsAppWebhookController][handleWebhook] Processing incoming message");
        return await this.processIncomingMessage(body);
      }

      console.log("[WhatsAppWebhookController][handleWebhook] Unhandled webhook event type");
      return {
        statusCode: 200,
        body: JSON.stringify({ message: "Event received but not processed" }),
      };

    } catch (error) {
      console.error("[WhatsAppWebhookController][handleWebhook] Error processing webhook:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({ message: "Internal server error" }),
      };
    }
  }

  /**
   * Processes incoming WhatsApp messages by forwarding to gateway controller
   */
  private async processIncomingMessage(webhookBody: any): Promise<APIGatewayProxyResult> {
    let messageId = "unknown";
    try {
      // Parse message data from webhook
      const messageData = this.messageParser.parseIncomingMessage(webhookBody);
      messageId = messageData.messageId;

      console.log("[WhatsAppWebhookController][processIncomingMessage] Parsed message data:", {
        userId: messageData.userId,
        whatsAppNumber: messageData.whatsAppNumber,
        messageId: messageData.messageId,
        messageType: messageData.messageType,
        fullName: messageData.fullName
      });

      // Create a gateway request in the format expected by the gateway controller
      const gatewayRequest = {
        messageId: messageData.messageId,
        userId: messageData.userId,
        userFullName: messageData.fullName,
        userWhatsAppNumber: messageData.whatsAppNumber,
        messageType: messageData.messageType,
        messageContent: messageData.messageContent,
        messagePlatform: MessagePlatform.WHATSAPP,
        messageOrigin: MessageOrigin.USER
      };

      // Create a mock API Gateway event for the gateway controller
      const mockEvent: APIGatewayProxyEvent = {
        body: JSON.stringify(gatewayRequest),
        headers: {},
        multiValueHeaders: {},
        httpMethod: "POST",
        isBase64Encoded: false,
        path: "/gateway/message",
        pathParameters: null,
        queryStringParameters: null,
        multiValueQueryStringParameters: null,
        stageVariables: null,
        requestContext: {} as any,
        resource: ""
      };

      console.log(`[WhatsAppWebhookController][processIncomingMessage] tid=${messageId} Forwarding to gateway controller`);

      // Forward the message to the gateway controller
      const gatewayResponse = await this.gatewayController.handleMessage(mockEvent);

      console.log(`[WhatsAppWebhookController][processIncomingMessage] tid=${messageId} Gateway response:`, {
        statusCode: gatewayResponse.statusCode,
        body: gatewayResponse.body
      });

      return gatewayResponse;

    } catch (error) {
      console.error(`[WhatsAppWebhookController][processIncomingMessage] tid=${messageId} Error:`, error);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Error processing WhatsApp message",
          error: error instanceof Error ? error.message : "Unknown error"
        }),
      };
    }
  }
}
