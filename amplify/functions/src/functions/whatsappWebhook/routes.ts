import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { WhatsAppWebhookController } from "./controllers/whatsappWebhookController";

const whatsappWebhookController = new WhatsAppWebhookController();

export const router = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const path = event.path.toLowerCase();
  const method = event.httpMethod.toUpperCase();

  // Handle webhook verification (GET request)
  if (method === "GET") {
    return await whatsappWebhookController.verifyWebhook(event);
  }

  // Handle webhook events (POST request)
  if (method === "POST") {
    return await whatsappWebhookController.handleWebhook(event);
  }

  return {
    statusCode: 405,
    body: JSON.stringify({ message: "Method not allowed" })
  };
};
