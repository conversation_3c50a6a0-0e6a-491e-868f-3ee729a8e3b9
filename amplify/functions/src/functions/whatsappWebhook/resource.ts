import { defineFunction } from "@aws-amplify/backend";

export const whatsappWebhookController = defineFunction({
  name: "whatsappWebhookController",
  entry: "./handler.ts",
  timeoutSeconds: 300,
  environment: {
    WHATSAPP_VERIFY_TOKEN: process.env.WHATSAPP_VERIFY_TOKEN ?? "default_verify_token",
    WHATSAPP_ACCESS_TOKEN: process.env.WHATSAPP_ACCESS_TOKEN ?? "EAA1y2Bhk2S0BPXx0nmkkoZABMRd3yQYZBo2NQgF5EeuxrYTywdf4I0ZBSM3238UYhXkqVZANWYdqF39xsRae8cojzUf8M7JmLrRdo3yVoIiZADbpTrkFihAdHtxOHZBvNDPwSpQl22uoOAEsuBsHowtgBUzdwxh3TUXSnHKE9WcesJXtfZAdIwnjjsa5mhszgZDZD",
    WHATSAPP_PHONE_NUMBER_ID: process.env.WHATSAPP_PHONE_NUMBER_ID ?? "440678912463914",
  },
});
